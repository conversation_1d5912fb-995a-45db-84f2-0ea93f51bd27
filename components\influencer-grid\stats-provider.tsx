import { getCachedUserStats } from '@/lib/stats-server-cache'

// Tipos para estatísticas otimizadas
interface BaseStats {
  totalInfluencers: number;
  totalViews: number;
  totalFollowers: number;
  totalBrands: number;
}

// 🚀 OTIMIZAÇÃO: Server Component otimizado com cache do Next.js

export async function StatsProvider({
  userId,
  children
}: {
  userId: string;
  children: (stats: BaseStats) => React.ReactNode;
}) {
  // 🚀 OTIMIZAÇÃO: Usar cache do Next.js com revalidação inteligente
  const baseStats = await getCachedUserStats(userId);

  return <>{children(baseStats)}</>;
}

// Hook para usar em Client Components (quando necessário)
export function useBaseStats(baseStats: BaseStats) {
  return baseStats;
}
