// TIPOS PARA SISTEMA DE ORÇAMENTOS
// Interface completa para orçamentos com relacionamento marca-influenciador

export interface Budget {
  id?: string;
  
  // Relacionamentos obrigatórios
  influencerId: string;
  influencerName: string;
  userId: string;           // Owner do orçamento
  brandId: string;          // ✅ MARCA ESPECÍFICA - OBRIGATÓRIO
  brandName?: string;       // Nome da marca para facilitar consultas
  proposalId?: string;      // ⚠️ OPCIONAL - Pode existir orçamento sem proposta
  proposalName?: string;    // ⚠️ OPCIONAL - Nome da proposta para contexto quando existir
  
  // Dados do orçamento
  amount: number;
  currency: string;
  description?: string;
  serviceType: ServiceType;
  
  // Serviços detalhados (para orçamentos complexos)
  services?: ServiceBudget;
  
  // Workflow e status
  status: BudgetStatus;
  expiresAt?: Date;
  
  // Contrapropostas
  hasCounterProposal?: boolean;
  counterProposals?: CounterProposal[];
  
  // 🔥 NOVO: Contrapropostas de colaboradores
  hasCollaboratorCounterProposals?: boolean;
  collaboratorCounterProposals?: CollaboratorCounterProposal[];
  
  // Metadados
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
}

export type ServiceType =
  | 'personalizado'
  | 'package' // Pacote de serviços
  // Formatos originais (camelCase)
  | 'instagramStory'
  | 'instagramReel'
  | 'instagramPost'
  | 'youtubeInsertion'
  | 'youtubeDedicated'
  | 'youtubeShorts'
  | 'tiktokVideo'
  | 'facebookPost'
  | 'twitchStream'
  | 'kwaiVideo'
  // Formatos com underscore (snake_case)
  | 'instagram_story'
  | 'instagram_reel'
  | 'instagram_post'
  | 'youtube_insertion'
  | 'youtube_dedicated'
  | 'youtube_shorts'
  | 'tiktok_video'
  | 'facebook_post'
  | 'twitch_stream'
  | 'kwai_video'
  // Formatos alternativos para compatibilidade
  | 'youtube_video'
  | 'instagram_stories'
  | 'youtube_longform';

export type BudgetStatus =
  | 'draft'           // Rascunho
  | 'sent'            // Enviado para análise
  | 'pending'         // Pendente de resposta
  | 'negotiating'     // Em negociação
  | 'approved'        // Aprovado
  | 'accepted'        // Aceito
  | 'rejected'        // Rejeitado
  | 'expired'         // Expirado
  | 'cancelled';      // Cancelado

export interface ServiceBudget {
  instagram?: {
    story?: PlatformPrice;
    reel?: PlatformPrice;
    post?: PlatformPrice;
  };
  youtube?: {
    shorts?: PlatformPrice;
    insertion?: PlatformPrice;
    dedicated?: PlatformPrice;
  };
  tiktok?: {
    video?: PlatformPrice;
  };
  facebook?: {
    post?: PlatformPrice;
    story?: PlatformPrice;
  };
  twitch?: {
    stream?: PlatformPrice;
  };
  kwai?: {
    video?: PlatformPrice;
  };
}

export interface PlatformPrice {
  price: number;
  currency: string;
  quantity?: number;        // Quantidade de conteúdos
  deliveryTime?: number;    // Tempo de entrega em dias
  notes?: string;
}

export interface CounterProposal {
  id?: string;
  budgetId: string;
  
  // Proposta alternativa
  proposedAmount: number;
  proposedServices?: ServiceBudget;
  proposalNotes?: string;
  quantity?: number; // Quantidade do serviço na contraproposta
  
  // Workflow
  status: 'pending' | 'accepted' | 'rejected';
  proposedBy: 'brand' | 'influencer';
  
  // Metadados
  createdAt: Date;
  respondedAt?: Date;
}

// 🔥 NOVO: Interface para contrapropostas de colaboradores
export interface CollaboratorCounterProposal {
  id?: string;
  budgetId: string;
  proposalId: string;
  influencerId: string;
  
  // Dados da contraproposta
  originalAmount: number;
  proposedAmount: number;
  currency: string;
  notes?: string;
  serviceType: string;
  
  // Colaborador que fez a contraproposta
  proposedBy: {
    userId: string;
    userName: string;
    userEmail: string;
    collaboratorRole: 'editor' | 'viewer';
  };
  
  // Status e workflow
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  
  // Metadados
  createdAt: Date;
  updatedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  reviewNote?: string;
}

// Input para criação de contraproposta de colaborador
export interface CreateCollaboratorCounterProposalInput {
  budgetId: string;
  proposalId: string;
  influencerId: string;
  influencerName: string;
  
  originalAmount: number;
  proposedAmount: number;
  currency: string;
  notes?: string;
  serviceType: string;
  
  // Dados do colaborador (serão preenchidos automaticamente)
  proposedBy: {
    userId: string;
    userName: string;
    userEmail: string;
    collaboratorRole: 'editor' | 'viewer';
  };
}

// Input para criação de orçamento
export interface CreateBudgetInput {
  influencerId: string;
  influencerName: string;
  userId: string;
  brandId: string;          // ✅ OBRIGATÓRIO
  proposalId?: string;      // ⚠️ OPCIONAL - Pode criar orçamento sem proposta
  proposalName?: string;    // ⚠️ OPCIONAL - Nome da proposta para contexto quando existir
  
  amount: number;
  currency?: string;
  description?: string;
  serviceType: ServiceType;
  services?: ServiceBudget;
  
  expiresAt?: Date;
}

// Input para atualização de orçamento
export interface UpdateBudgetInput {
  amount?: number;
  currency?: string;
  description?: string;
  serviceType?: ServiceType;
  services?: ServiceBudget;
  status?: BudgetStatus;
  expiresAt?: Date;
}

// Filtros para busca de orçamentos
export interface BudgetFilters {
  influencerId?: string;
  brandId?: string;
  userId?: string;
  status?: BudgetStatus[];
  serviceType?: ServiceType[];
  minAmount?: number;
  maxAmount?: number;
  createdAfter?: Date;
  createdBefore?: Date;
  hasCounterProposal?: boolean;
}

// Estatísticas de orçamentos
export interface BudgetStats {
  totalBudgets: number;
  totalValue: number;
  averageValue: number;
  byStatus: { [status in BudgetStatus]: number };
  byServiceType: { [serviceType in ServiceType]: number };
  byBrand: { brandId: string; brandName: string; count: number; totalValue: number }[];
  conversionRate: number;
} 

