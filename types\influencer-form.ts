import { z } from 'zod'

// 🎯 Estrutura base para audiência
export const AudienceGenderSchema = z.object({
  male: z.number().min(0).max(100),
  female: z.number().min(0).max(100),
  other: z.number().min(0).max(100),
})

export const AudienceLocationSchema = z.object({
  country: z.string(),
  percentage: z.number().min(0).max(100),
})

export const AudienceCitySchema = z.object({
  city: z.string(),
  percentage: z.number().min(0).max(100),
})

export const AudienceAgeRangeSchema = z.object({
  range: z.string(),
  percentage: z.number().min(0).max(100),
})

// 🌐 Estrutura unificada por plataforma
export const SocialPlatformSchema = z.object({
  // Dados básicos
  username: z.string().optional(),
  followers: z.number().min(0).optional().default(0),
  engagementRate: z.number().optional().transform((val) => {
    // 🔧 CORREÇÃO: Se o valor estiver fora do range, ajustar automaticamente
    if (val === undefined || val === null) return undefined;
    if (val < 0) return 0;
    if (val > 100) return 100;
    return val;
  }),
  
  // Audiência
  audienceGender: AudienceGenderSchema.optional(),
  audienceLocations: z.array(AudienceLocationSchema).optional(),
  audienceCities: z.array(AudienceCitySchema).optional(), // 🏙️ Cidades separadas dos países
  audienceAgeRange: z.array(AudienceAgeRangeSchema).optional(),
  
  // Preços específicos por plataforma
  pricing: z.record(z.string(), z.number()).optional(),
  
  // Métricas específicas
  metrics: z.record(z.string(), z.number()).optional(),
  
  // Histórico de marcas
  brandHistory: z.array(z.string()).optional(),
  
  // Screenshots
  screenshots: z.array(z.string()).optional(),
})

// 📱 Configuração específica por rede social
export const PlatformConfigSchema = z.object({
  instagram: SocialPlatformSchema.extend({
    pricing: z.object({
      story: z.number().optional(),
      reel: z.number().optional(),
    }).optional(),
    metrics: z.object({
      storiesViews: z.number().optional(),
      reelsViews: z.number().optional(),
    }).optional(),
    views: z.object({
      storiesViews: z.number().optional(),
      reelsViews: z.number().optional(),
    }).optional(),
  }).optional(),
  
  youtube: SocialPlatformSchema.extend({
    pricing: z.object({
      shorts: z.number().optional(),
      dedicated: z.number().optional(),
      insertion: z.number().optional(),
    }).optional(),
    metrics: z.object({
      shortsViews: z.number().optional(),
      longFormViews: z.number().optional(),
    }).optional(),
    views: z.object({
      shortsViews: z.number().optional(),
      longFormViews: z.number().optional(),
    }).optional(),
  }).optional(),
  
  tiktok: SocialPlatformSchema.extend({
    pricing: z.object({
      video: z.number().optional(),
    }).optional(),
    views: z.object({
      videoViews: z.number().optional(),
    }).optional(),
  }).optional(),
  
  facebook: SocialPlatformSchema.extend({
    pricing: z.object({
      post: z.number().optional(),
    }).optional(),
    views: z.object({
      reelsViews: z.number().optional(),
      storiesViews: z.number().optional(),
    }).optional(),
    avgViews: z.number().optional(),
  }).optional(),
  
  twitch: SocialPlatformSchema.extend({
    pricing: z.object({
      stream: z.number().optional(),
    }).optional(),
    avgViews: z.number().optional(),
  }).optional(),
  
  kwai: SocialPlatformSchema.extend({
    pricing: z.object({
      video: z.number().optional(),
    }).optional(),
    avgViews: z.number().optional(),
  }).optional(),
})

// 👤 Dados principais do influenciador (CAMPOS FLEXÍVEIS)
export const InfluencerFormSchema = z.object({
  // ID do documento (para edição)
  id: z.string().optional(),
  
  // Informações pessoais - TODOS OPCIONAIS
  personalInfo: z.object({
    name: z.string().optional(),
    age: z.number().min(13).max(100).optional(),
    gender: z.enum(["Masculino", "Feminino"]).optional(),
    bio: z.string().optional(),
    avatar: z.string().refine((val) => {
      if (!val) return true; // Opcional
      return val.startsWith('https://') || val.startsWith('data:image/');
    }, "Avatar deve ser uma URL válida ou data URL").optional(),
    avatarFile: z.any().optional(), // Arquivo temporário para upload posterior
    verified: z.boolean().default(false),
  }),
  
  // Localização - TODOS OPCIONAIS
  location: z.object({
    country: z.string().default("Brasil"),
    state: z.string().optional(),
    city: z.string().optional(),
    cep: z.string().optional(), // Removido length(8) para ser mais flexível
  }),
  
  // Contato - TODOS OPCIONAIS
  contact: z.object({
    email: z.string().email("Email inválido").or(z.literal("")).optional(),
    whatsapp: z.string().optional(),
  }),
  
  // Negócios - TODOS OPCIONAIS
  business: z.object({
    agencyName: z.string().optional(),
    responsibleName: z.string().optional(),
    responsibleCapturer: z.string().optional(),
    categories: z.array(z.string()).optional(),
    promotesTraders: z.boolean().default(false),
    brandPartnerships: z.array(z.object({
      brandName: z.string(),
      description: z.string().optional(),
    })).default([]),
  }),
  
  // Marcas associadas - OPCIONAL
  brands: z.array(z.string()).optional().default([]),
  
  // Redes sociais - OPCIONAIS - Permite objeto vazio
  platforms: PlatformConfigSchema.optional().or(z.object({})),
  mainPlatform: z.enum(["instagram", "youtube", "tiktok", "facebook", "twitch", "kwai"]).optional(),
  
  // Métricas calculadas - OPCIONAIS
  metrics: z.object({
    totalFollowers: z.number().min(0).default(0),
    overallEngagementRate: z.number().default(0).transform((val) => {
      // 🔧 CORREÇÃO: Se o valor estiver fora do range, ajustar automaticamente
      if (val === undefined || val === null) return 0;
      if (val < 0) return 0;
      if (val > 100) return 100;
      return val;
    }),
  }).optional(),

  // Screenshots pendentes para upload
  pendingScreenshots: z.array(z.object({
    id: z.string(),
    file: z.any(),
    preview: z.string(),
    platform: z.string(),
  })).optional(),
})

// 🔧 Tipos derivados
export type InfluencerFormData = z.infer<typeof InfluencerFormSchema>
export type SocialPlatform = z.infer<typeof SocialPlatformSchema>
export type AudienceGender = z.infer<typeof AudienceGenderSchema>
export type AudienceLocation = z.infer<typeof AudienceLocationSchema>
export type AudienceCity = z.infer<typeof AudienceCitySchema>
export type AudienceAgeRange = z.infer<typeof AudienceAgeRangeSchema>

// 🎨 Configuração de UI por plataforma
export const PLATFORM_CONFIG = {
  instagram: {
    name: "Instagram",
    icon: "Instagram",
    color: "#E4405F",
    fields: ["story", "reel"],
    metrics: ["storiesViews", "reelsViews"],
    viewsFields: [
      { key: "storiesViews", label: "Visualizações nos Stories" },
      { key: "reelsViews", label: "Visualizações nos Reels" }
    ],
  },
  youtube: {
    name: "YouTube", 
    icon: "Youtube",
    color: "#FF0000",
    fields: ["shorts", "dedicated", "insertion"],
    metrics: ["shortsViews", "longFormViews"],
    viewsFields: [
      { key: "shortsViews", label: "Visualizações no Shorts" },
      { key: "longFormViews", label: "Visualizações em vídeo longo" }
    ],
  },
  tiktok: {
    name: "TikTok",
    icon: "Music", 
    color: "#000000",
    fields: ["video"],
    metrics: [],
    viewsFields: [
      { key: "videoViews", label: "Visualizações no TikTok" }
    ],
  },
  facebook: {
    name: "Facebook",
    icon: "Facebook",
    color: "#1877F2", 
    fields: ["post"],
    metrics: ["reelsViews", "storiesViews"],
    viewsFields: [
      { key: "reelsViews", label: "Visualizações nos Reels" },
      { key: "storiesViews", label: "Visualizações nos Stories" }
    ],
  },
  twitch: {
    name: "Twitch",
    icon: "Twitch",
    color: "#9146FF",
    fields: ["stream"],
    metrics: [],
    viewsFields: [
      { key: "avgViews", label: "twitchviews" }
    ],
  },
  kwai: {
    name: "Kwai",
    icon: "Video",
    color: "#FF6600",
    fields: ["video"],
    metrics: [],
    viewsFields: [
      { key: "avgViews", label: "kwai views" }
    ],
  },
} as const 

