import { useMemo } from "react"
import { useTranslations } from "@/hooks/use-translations"
import { UnifiedFilterToolbar } from "./unified-filter-toolbar"

// 🚀 OTIMIZAÇÃO: Interface para dados de influencers do GraphQL
interface InfluencerData {
  id: string;
  // Seguidores por plataforma
  instagramFollowers?: number;
  tiktokFollowers?: number;
  youtubeFollowers?: number;
  youtubeSubscribers?: number;
  facebookFollowers?: number;
  twitchFollowers?: number;
  kwaiFollowers?: number;
  // Views por plataforma
  instagramAvgViews?: number;
  instagramStoriesViews?: number;
  instagramReelsViews?: number;
  tiktokAvgViews?: number;
  tiktokVideoViews?: number;
  youtubeAvgViews?: number;
  youtubeShortsViews?: number;
  youtubeLongFormViews?: number;
  facebookAvgViews?: number;
  facebookViews?: number;
  twitchViews?: number;
  kwaiViews?: number;
  // Campos de compatibilidade
  totalFollowers?: number;
  totalViews?: number;
}

interface AnimatedStatsProps {
  // 🚀 NOVO: Array de influencers para cálculo local (RECOMENDADO)
  influencers?: InfluencerData[];
  // 🚀 NOVO: Dados filtrados para cálculo preciso
  filteredInfluencers?: InfluencerData[];
  
  // 🔄 COMPATIBILIDADE: Props antigas (ainda funcionam)
  influencersCount?: number;
  userId?: string;
  
  // Props para FilterBar
  searchTerm?: string;
  selectedCategory?: string;
  onSearchChange?: (value: string) => void;
  onCategoryChange?: (category: string) => void;
  // Props para Toolbar
  viewMode?: "grid" | "list";
  selectionMode?: boolean;
  selectedCount?: number;
  onViewModeChange?: (mode: "grid" | "list") => void;
  onAddInfluencer?: () => void;
  onToggleSelectionMode?: () => void;
  onDeleteSelected?: () => void;
  onDuplicateSelected?: () => void;
  onBrandFilterChange?: (selectedBrands: string[]) => void;
  verifiedOnly?: boolean;
  onVerifiedOnlyChange?: (value: boolean) => void;
  onSendToBrand?: (brandId: string, brandName: string) => void;
}

// 🚀 COMPONENTE OTIMIZADO: Drop-in replacement para AnimatedStats
// 🔧 FUNÇÃO UTILITÁRIA: Parse seguro de números com limite máximo
function parseNumber(value: any): number {
  if (typeof value === 'number' && !isNaN(value)) {
    // Limitar a números razoáveis (máximo 1 bilhão)
    return Math.max(0, Math.min(value, 1000000000));
  }

  if (typeof value === 'string') {
    // Remover caracteres não numéricos exceto pontos e vírgulas
    const cleanValue = value.replace(/[^\d.,]/g, '');
    const parsed = parseFloat(cleanValue.replace(',', '.'));
    if (isNaN(parsed)) return 0;

    // Limitar a números razoáveis (máximo 1 bilhão)
    return Math.max(0, Math.min(parsed, 1000000000));
  }

  return 0;
}

export function AnimatedStats({
  influencers = [],
  filteredInfluencers,
  influencersCount,
  userId,
  searchTerm,
  selectedCategory,
  onSearchChange,
  onCategoryChange,
  // Props da Toolbar
  viewMode,
  selectionMode,
  selectedCount,
  onViewModeChange,
  onAddInfluencer,
  onToggleSelectionMode,
  onDeleteSelected,
  onDuplicateSelected,
  onBrandFilterChange,
  verifiedOnly,
  onVerifiedOnlyChange,
  onSendToBrand
}: AnimatedStatsProps) {
  const { t } = useTranslations();

  // 🚀 FUNÇÃO OTIMIZADA: Calcular estatísticas localmente dos dados GraphQL
  const calculateStatsFromInfluencers = useMemo(() => {
    return (influencersList: InfluencerData[]) => {
      if (!influencersList || influencersList.length === 0) {
        return { totalInfluencers: 0, totalViews: 0, totalFollowers: 0 };
      }

      let totalFollowers = 0;
      let totalViews = 0;

      influencersList.forEach(influencer => {
        // 📊 SOMAR SEGUIDORES de todas as plataformas (com validação)
        const followers =
          parseNumber(influencer.instagramFollowers) +
          parseNumber(influencer.tiktokFollowers) +
          parseNumber(influencer.youtubeFollowers || influencer.youtubeSubscribers) +
          parseNumber(influencer.facebookFollowers) +
          parseNumber(influencer.twitchFollowers) +
          parseNumber(influencer.kwaiFollowers);

        // 📊 SOMAR VIEWS de todas as plataformas (com validação)
        const views =
          // Instagram views (múltiplos tipos)
          parseNumber(influencer.instagramAvgViews) +
          parseNumber(influencer.instagramStoriesViews) +
          parseNumber(influencer.instagramReelsViews) +
          // TikTok views
          parseNumber(influencer.tiktokAvgViews) +
          parseNumber(influencer.tiktokVideoViews) +
          // YouTube views (múltiplos tipos)
          parseNumber(influencer.youtubeAvgViews) +
          parseNumber(influencer.youtubeShortsViews) +
          parseNumber(influencer.youtubeLongFormViews) +
          // Outras plataformas
          parseNumber(influencer.facebookAvgViews || influencer.facebookViews) +
          parseNumber(influencer.twitchViews) +
          parseNumber(influencer.kwaiViews);

        // 🔄 FALLBACK: Usar campos totais se disponíveis e individuais não existirem
        const fallbackFollowers = parseNumber(influencer.totalFollowers);
        const fallbackViews = parseNumber(influencer.totalViews);

        totalFollowers += followers > 0 ? followers : fallbackFollowers;
        totalViews += views > 0 ? views : fallbackViews;
      });

      return {
        totalInfluencers: influencersList.length,
        totalViews: Math.round(totalViews),
        totalFollowers: Math.round(totalFollowers)
      };
    };
  }, []);

  // Detectar se há filtros ativos
  const hasActiveFilters = useMemo(() => {
    const hasSearch = searchTerm && searchTerm.trim().length > 0;
    const hasCategory = selectedCategory && selectedCategory !== 'all' && selectedCategory !== '';
    const hasVerified = verifiedOnly === true;
    
    return hasSearch || hasCategory || hasVerified;
  }, [searchTerm, selectedCategory, verifiedOnly]);

  // 🚀 CALCULAR ESTATÍSTICAS: Priorizar dados locais, fallback para compatibilidade
  const displayStats = useMemo(() => {
    // 🎯 MODO OTIMIZADO: Se temos dados de influencers, calcular localmente
    if (influencers && influencers.length > 0) {
      if (hasActiveFilters && filteredInfluencers && filteredInfluencers.length > 0) {
        // Usar dados filtrados
        const stats = calculateStatsFromInfluencers(filteredInfluencers);
        return {
          ...stats,
          isFiltered: true,
          sourceType: 'filtered-local'
        };
      } else {
        // Usar dados totais
        const stats = calculateStatsFromInfluencers(influencers);
        return {
          ...stats,
          isFiltered: false,
          sourceType: 'total-local'
        };
      }
    }

    // 🔄 MODO COMPATIBILIDADE: Fallback para props antigas
    return {
      totalInfluencers: influencersCount || 0,
      totalViews: 0, // Não disponível no modo compatibilidade
      totalFollowers: 0, // Não disponível no modo compatibilidade
      isFiltered: hasActiveFilters,
      sourceType: 'compatibility-mode'
    };
  }, [
    influencers, 
    filteredInfluencers, 
    hasActiveFilters, 
    influencersCount, 
    calculateStatsFromInfluencers
  ]);

  // Formatador para exibir números com separação de milhares
  const formatNumber = (num: number): string => {
    if (typeof num !== 'number' || isNaN(num)) {
      return '0';
    }
    return num.toLocaleString('pt-BR');
  };

  return (
    <div className="sticky top-0 z-5 w-full bg-background backdrop-blur-sm border-b border-border/40 ">
      <div className="px-2 py-4 space-y-4">
        {/* Estatísticas */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-6">
            
            {/* Estatística de Influencers */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-influencers)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-influencers" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8zm0-2a2 2 0 1 1 0-4 2 2 0 0 1 0 4z" />
                <path d="M12 12c-5.5 0-10 3.5-10 8v1h20v-1c0-4.5-4.5-8-10-8zm0 2c4.5 0 8 2.5 8 6H4c0-3.5 3.5-6 8-6z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalInfluencers)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">
                {displayStats.isFiltered ? 
                  String(t('common.found')).toUpperCase() : 
                  String(t('navigation.influencers')).toUpperCase()
                }
              </span>
              {displayStats.isFiltered && (
                <span className="text-xs text-muted-foreground ml-1">(filtrados)</span>
              )}
            </div>
            
            <div className="text-muted-foreground dark:text-white">|</div>
            
            {/* Estatística de Views */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-views)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-views" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalViews)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">{String(t('panels.contact.total_views')).toUpperCase()}</span>
            </div>
            
            <div className="text-muted-foreground dark:text-white">|</div>
            
            {/* Estatística de Followers */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-followers)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-followers" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalFollowers)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">{String(t('panels.contact.total_followers')).toUpperCase()}</span>
            </div>

           
            
          </div>
        </div>

        {/* Filtros e Toolbar */}
        <UnifiedFilterToolbar
          searchTerm={searchTerm || ''}
          selectedCategory={selectedCategory || ''}
          onSearchChange={onSearchChange || (() => {})}
          onCategoryChange={onCategoryChange || (() => {})}
          userId={userId} // 🔧 CORREÇÃO: Passar userId para carregar categorias
          viewMode={viewMode || 'grid'}
          selectionMode={selectionMode || false}
          selectedCount={selectedCount || 0}
          onViewModeChange={onViewModeChange || (() => {})}
          onAddInfluencer={onAddInfluencer || (() => {})}
          onToggleSelectionMode={onToggleSelectionMode || (() => {})}
          onDeleteSelected={onDeleteSelected || (() => {})}
          onDuplicateSelected={onDuplicateSelected || (() => {})}
          onBrandFilterChange={onBrandFilterChange || (() => {})}
          verifiedOnly={verifiedOnly || false}
          onVerifiedOnlyChange={onVerifiedOnlyChange || (() => {})}
          onSendToBrand={onSendToBrand || (() => {})}
        />
      </div>
    </div>
  );
}
