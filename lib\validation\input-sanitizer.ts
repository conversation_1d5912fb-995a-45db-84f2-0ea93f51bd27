// Utilitário de Sanitização de Entrada - Prevenção Mass Assignment
// Garante que apenas campos permitidos sejam processados pelas APIs

export interface FieldValidator {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date';
  required?: boolean;
  allowedValues?: any[];
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
}

export interface ValidationSchema {
  [fieldName: string]: FieldValidator;
}

export class InputSanitizer {
  
  /**
   * Sanitizar e validar dados de entrada contra um schema
   * PREVINE: Mass Assignment, Type Confusion, Injection
   */
  static sanitizeInput<T = any>(
    inputData: any, 
    schema: ValidationSchema,
    strict: boolean = true
  ): { valid: boolean; data: T; errors: string[] } {
    
    const sanitizedData: any = {};
    const errors: string[] = [];
    
    // Verificar campos obrigatórios
    Object.entries(schema).forEach(([fieldName, validator]) => {
      if (validator.required && (inputData[fieldName] === undefined || inputData[fieldName] === null)) {
        errors.push(`Campo obrigatório '${fieldName}' não fornecido`);
      }
    });
    
    // Processar apenas campos definidos no schema
    Object.entries(schema).forEach(([fieldName, validator]) => {
      const value = inputData[fieldName];
      
      // Pular campos undefined (se não obrigatórios)
      if (value === undefined) {
        return;
      }
      
      // Validar tipo
      const typeValidation = this.validateType(value, validator.type);
      if (!typeValidation.valid) {
        errors.push(`Campo '${fieldName}': ${typeValidation.error}`);
        return;
      }
      
      // Validar valores permitidos
      if (validator.allowedValues && !validator.allowedValues.includes(value)) {
        errors.push(`Campo '${fieldName}': valor '${value}' não é permitido. Valores aceitos: ${validator.allowedValues.join(', ')}`);
        return;
      }
      
      // Validações específicas por tipo
      if (validator.type === 'string') {
        if (validator.minLength && value.length < validator.minLength) {
          errors.push(`Campo '${fieldName}': deve ter pelo menos ${validator.minLength} caracteres`);
          return;
        }
        if (validator.maxLength && value.length > validator.maxLength) {
          errors.push(`Campo '${fieldName}': deve ter no máximo ${validator.maxLength} caracteres`);
          return;
        }
        if (validator.pattern && !validator.pattern.test(value)) {
          errors.push(`Campo '${fieldName}': formato inválido`);
          return;
        }
      }
      
      if (validator.type === 'number') {
        if (validator.min !== undefined && value < validator.min) {
          errors.push(`Campo '${fieldName}': deve ser maior ou igual a ${validator.min}`);
          return;
        }
        if (validator.max !== undefined && value > validator.max) {
          errors.push(`Campo '${fieldName}': deve ser menor ou igual a ${validator.max}`);
          return;
        }
      }
      
      // Se chegou até aqui, campo é válido
      sanitizedData[fieldName] = value;
    });
    
    // Em modo strict, rejeitar campos extras
    if (strict) {
      Object.keys(inputData).forEach(key => {
        if (!schema[key]) {
          errors.push(`Campo não permitido: '${key}'`);
        }
      });
    }
    
    return {
      valid: errors.length === 0,
      data: sanitizedData as T,
      errors
    };
  }
  
  /**
   * Validar tipo específico
   */
  private static validateType(value: any, expectedType: string): { valid: boolean; error?: string } {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          return { valid: false, error: `esperado string, recebido ${typeof value}` };
        }
        break;
        
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return { valid: false, error: `esperado number, recebido ${typeof value}` };
        }
        break;
        
      case 'boolean':
        if (typeof value !== 'boolean') {
          return { valid: false, error: `esperado boolean, recebido ${typeof value}` };
        }
        break;
        
      case 'array':
        if (!Array.isArray(value)) {
          return { valid: false, error: `esperado array, recebido ${typeof value}` };
        }
        break;
        
      case 'object':
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
          return { valid: false, error: `esperado object, recebido ${typeof value}` };
        }
        break;
        
      case 'date':
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return { valid: false, error: `esperado date válida, recebido ${value}` };
        }
        break;
        
      default:
        return { valid: false, error: `tipo '${expectedType}' não reconhecido` };
    }
    
    return { valid: true };
  }
}

// Schema para validação de orçamentos
export const BUDGET_UPDATE_SCHEMA: ValidationSchema = {
  amount: {
    type: 'number',
    min: 0,
    max: 1000000
  },
  currency: {
    type: 'string',
    allowedValues: ['BRL', 'USD', 'EUR'],
    maxLength: 3
  },
  description: {
    type: 'string',
    maxLength: 1000
  },
  serviceType: {
    type: 'string',
    allowedValues: [
      'personalizado', 'package', 'instagramStory', 'instagramReel', 'instagramPost',
      'youtubeInsertion', 'youtubeDedicated', 'youtubeShorts',
      'tiktokVideo', 'facebookPost', 'twitchStream', 'kwaiVideo',
      'instagram_story', 'instagram_reel', 'instagram_post',
      'youtube_insertion', 'youtube_dedicated', 'youtube_shorts',
      'tiktok_video', 'facebook_post', 'twitch_stream', 'kwai_video',
      'youtube_video', 'instagram_stories', 'youtube_longform'
    ]
  },
  status: {
    type: 'string',
    allowedValues: ['draft', 'sent', 'pending', 'negotiating', 'approved', 'accepted', 'rejected', 'expired', 'cancelled']
  }
};

// Schema para validação de contrapropostas
export const COUNTER_PROPOSAL_SCHEMA: ValidationSchema = {
  originalAmount: {
    type: 'number',
    required: true,
    min: 0
  },
  proposedAmount: {
    type: 'number',
    required: true,
    min: 0,
    max: 1000000
  },
  currency: {
    type: 'string',
    required: true,
    allowedValues: ['BRL', 'USD', 'EUR']
  },
  notes: {
    type: 'string',
    maxLength: 2000
  },
  serviceType: {
    type: 'string',
    required: true
  }
}; 

