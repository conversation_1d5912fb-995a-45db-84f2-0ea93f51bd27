import { NextRequest, NextResponse } from 'next/server'
import { revalidateTag } from 'next/cache'
import { invalidateStatsCache } from '@/lib/stats-server-cache'

// 🚀 API para revalidar cache de estatísticas de forma otimizada
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, action, secret } = body;

    // Validação básica de segurança
    if (secret !== process.env.REVALIDATE_SECRET) {
      return NextResponse.json(
        { error: 'Token de segurança inválido' },
        { status: 401 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'revalidate':
        // Apenas revalidar cache do Next.js (mais rápido)
        revalidateTag('user-stats');
        result = { message: 'Cache revalidado com sucesso', userId };
        break;
        
      case 'invalidate':
        // Invalidar completamente (remove dados denormalizados)
        const success = await invalidateStatsCache(userId);
        result = { 
          message: success ? 'Cache invalidado com sucesso' : 'Erro ao invalidar cache', 
          userId,
          success 
        };
        break;
        
      default:
        // Padrão: apenas revalidar
        revalidateTag('user-stats');
        result = { message: 'Cache revalidado (ação padrão)', userId };
        break;
    }

    console.log('✅ [StatsAPI] Operação concluída:', result);

    return NextResponse.json(result, { status: 200 });

  } catch (error) {
    console.error('❌ [StatsAPI] Erro na operação:', error);
    
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

// Método GET para verificar status da API
export async function GET() {
  return NextResponse.json({
    message: 'API de revalidação de estatísticas ativa',
    timestamp: new Date().toISOString(),
    endpoints: {
      POST: {
        description: 'Revalidar ou invalidar cache de estatísticas',
        parameters: {
          userId: 'string (obrigatório)',
          action: 'revalidate | invalidate (opcional, padrão: revalidate)',
          secret: 'string (obrigatório)'
        }
      }
    }
  });
}
