// HOOK CUSTOMIZADO PARA INFLUENCIADORES VIA GRAPHQL
// Gerenciamento completo de dados de influenciadores usando Apollo Client

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { gql } from '@apollo/client';
import { toast } from 'sonner';
import { useApolloClient } from '@apollo/client';

// ===== QUERIES GRAPHQL =====

const GET_INFLUENCERS = gql`
  query GetInfluencers($userId: ID!, $filters: InfluencerFilters, $pagination: PaginationInput) {
    influencers(userId: $userId, filters: $filters, pagination: $pagination) {
      totalCount
      hasNextPage
      hasPreviousPage
      nodes {
        id
        userId
        name
        email
        phone
        whatsapp
        country
        state
        city
        location
        age
        gender
        bio
        avatar
        backgroundImage
        gradient
        category
        categories
        totalFollowers
        totalViews
        engagementRate
        rating
        isVerified
        isAvailable
        status
        # Redes sociais (campos diretos)
        instagramUsername
        instagramFollowers
        instagramEngagementRate
        instagramAvgViews
        instagramStoriesViews
        instagramReelsViews
        
        tiktokUsername
        tiktokFollowers
        tiktokEngagementRate
        tiktokAvgViews
        tiktokVideoViews
        
        youtubeUsername
        youtubeFollowers
        youtubeSubscribers
        youtubeEngagementRate
        youtubeAvgViews
        youtubeShortsViews
        youtubeLongFormViews
        
        facebookUsername
        facebookFollowers
        facebookEngagementRate
        facebookAvgViews
        facebookViews
        facebookReelsViews
        facebookStoriesViews
        
        twitchUsername
        twitchFollowers
        twitchEngagementRate
        twitchViews
        
        kwaiUsername
        kwaiFollowers
        kwaiEngagementRate
        kwaiViews
        
        # Configurações profissionais
        promotesTraders
        responsibleName
        agencyName
        responsibleCapturer
        
        # Rede social principal
        mainNetwork
        mainPlatform
        
        # Pricing denormalizado básico
        pricing {
          hasFinancialData
          priceRange
          avgPrice
          lastPriceUpdate
          isNegotiable
        }
        
        # Pricing separado (nova estrutura) - dados básicos
        currentPricing {
          id
          services {
            instagram {
              story { price currency }
              reel { price currency }
            }
            tiktok {
              video { price currency }
            }
            youtube {
              insertion { price currency }
              dedicated { price currency }
              shorts { price currency }
            }
            facebook {
              post { price currency }
            }
            twitch {
              stream { price currency }
            }
            kwai {
              video { price currency }
            }
          }
          isActive
          validFrom
          validUntil
        }
        
        # Demographics separado (nova estrutura) - dados básicos
        currentDemographics {
          id
          platform
          audienceGender {
            male
            female
            other
          }
          audienceLocations {
            country
            percentage
          }
          audienceCities {
            city
            percentage
          }
          audienceAgeRange {
            range
            percentage
          }
          isActive
          source
        }

        # 🔥 CORREÇÃO: Adicionar orçamentos organizados por plataforma
        budgets {
          instagram {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          tiktok {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          youtube {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          facebook {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          twitch {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          kwai {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          personalizado {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
        }
        
        createdAt
        updatedAt
      }
    }
  }
`;

const GET_INFLUENCER_BY_ID = gql`
  query GetInfluencer($id: ID!, $userId: ID!) {
    influencer(id: $id, userId: $userId) {
      id
      userId
      name
      email
      phone
      whatsapp
      country
      state
      city
      location
      age
      gender
      bio
      avatar
      backgroundImage
      gradient
      category
      categories
      totalFollowers
      totalViews
      engagementRate
      rating
      isVerified
      isAvailable
      status
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      promotesTraders
      responsibleName
      agencyName
      responsibleCapturer
      
      # Rede social principal
      mainNetwork
      
      # Pricing separado (nova estrutura)
      currentPricing {
        id
        services {
          instagram {
            story {
              price
              currency
            }
            reel {
              price
              currency
            }
          }
          tiktok {
            video {
              price
              currency
            }
          }
          youtube {
            insertion {
              price
              currency
            }
            dedicated {
              price
              currency
            }
            shorts {
              price
              currency
            }
          }
          facebook {
            post {
              price
              currency
            }
          }
          twitch {
            stream {
              price
              currency
            }
          }
          kwai {
            video {
              price
              currency
            }
          }
        }
        isActive
        validFrom
        validUntil
        notes
        createdAt
        updatedAt
      }
      
      # Demographics separado (nova estrutura)
      currentDemographics {
        id
        platform
        audienceGender {
          male
          female
          other
        }
        audienceLocations {
          country
          percentage
        }
        audienceCities {
          city
          percentage
        }
        audienceAgeRange {
          range
          percentage
        }
        captureDate
        isActive
        source
        createdAt
        updatedAt
      }
      
      # Fallback para estrutura antiga
      pricing {
        hasFinancialData
        priceRange
        avgPrice
        lastPriceUpdate
        isNegotiable
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_CACHE_STATS = gql`
  query GetCacheStats {
    cacheStats {
      hits
      misses
      evictions
      size
      hitRate
    }
  }
`;

// ===== MUTATIONS GRAPHQL =====

const CREATE_INFLUENCER = gql`
  mutation CreateInfluencer($input: CreateInfluencerInput!) {
    createInfluencer(input: $input) {
      id
      name
      email
      country
      state
      city
      gender
      category
      categories
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      
      youtubeUsername
      youtubeFollowers
      youtubeEngagementRate
      youtubeAvgViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      # 🔥 CORREÇÃO: Adicionar campos administrativos
      promotesTraders
      responsibleName
      agencyName
      responsibleCapturer
      
      # Rede social principal
      mainNetwork
      mainPlatform
      
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_INFLUENCER = gql`
  mutation UpdateInfluencer($id: ID!, $input: UpdateInfluencerInput!) {
    updateInfluencer(id: $id, input: $input) {
      id
      name
      email
      phone
      whatsapp
      country
      state
      city
      location
      age
      gender
      bio
      avatar
      category
      categories
      totalFollowers
      engagementRate
      isVerified
      isAvailable
      status
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      facebookViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiViews
      
      promotesTraders
      responsibleName
      agencyName
      responsibleCapturer
      mainNetwork
      mainPlatform
      updatedAt
    }
  }
`;

const DELETE_INFLUENCER = gql`
  mutation DeleteInfluencer($id: ID!) {
    deleteInfluencer(id: $id)
  }
`;

// ===== INTERFACES =====

interface InfluencerFilters {
  search?: string;
  category?: string;
  isAvailable?: boolean;
  followersMin?: number;
  followersMax?: number;
  priceRange?: string;
}

interface PaginationInput {
  limit?: number;
  offset?: number;
}

interface UseInfluencersOptions {
  userId: string;
  autoFetch?: boolean;
  filters?: InfluencerFilters;
  pagination?: PaginationInput;
  infiniteScroll?: boolean;
}

// ===== HOOK PRINCIPAL =====

export function useInfluencersGraphQL(options: UseInfluencersOptions) {
  const { userId, autoFetch = true, filters, pagination, infiniteScroll = true } = options;
  
  // Estados locais
  const [localInfluencers, setLocalInfluencers] = useState<any[]>([]);
  const [selectedInfluencer, setSelectedInfluencer] = useState<any | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  
  // 🔥 OTIMIZAÇÃO: Debounce para evitar requisições em rajada
  const [debouncedFilters, setDebouncedFilters] = useState(filters);
  
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 500); // 500ms de debounce
    
    return () => clearTimeout(timeoutId);
  }, [filters]);
  
  // Query principal para buscar influenciadores - OTIMIZADA
  const { 
    data: influencersData, 
    loading: influencersLoading, 
    error: influencersError,
    refetch: refetchInfluencers,
    fetchMore
  } = useQuery(GET_INFLUENCERS, {
    variables: { 
      userId, 
      filters: debouncedFilters || {}, // 🔥 OTIMIZAÇÃO: Usar filtros com debounce
      pagination: pagination || { limit: 20, offset: 0 }
    },
    skip: !autoFetch || !userId || userId === '',
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache (era cache-and-network)
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: false, // 🔥 OTIMIZAÇÃO: Reduzir re-renders
    onError: (error) => {
      // Log removido para reduzir volume de console
      toast.error('Erro ao carregar influenciadores via GraphQL');
    }
  });

  // 🔥 CORREÇÃO: Mover atualizações de estado para useEffect em vez de onCompleted
  useEffect(() => {
    if (influencersData?.influencers) {
      const currentOffset = pagination?.offset || 0;
      const newInfluencers = influencersData.influencers.nodes || [];
      
      if (currentOffset === 0 || !infiniteScroll) {
        setLocalInfluencers(newInfluencers);
      } else {
        setLocalInfluencers(prevInfluencers => {
          const existingIds = new Set(prevInfluencers.map((inf: any) => inf.id));
          const newUniqueInfluencers = newInfluencers.filter((inf: any) => !existingIds.has(inf.id));
          return [...prevInfluencers, ...newUniqueInfluencers];
        });
      }
      
      setHasMoreData(influencersData.influencers.hasNextPage);
    }
  }, [influencersData, pagination?.offset, infiniteScroll]);

  // 🔥 OTIMIZAÇÃO: Função loadMore com throttling
  const loadMore = useCallback(async () => {
    if (!hasMoreData || isLoadingMore || influencersLoading) {
      return;
    }

    setIsLoadingMore(true);

    try {
      const result = await fetchMore({
        variables: {
          userId,
          filters: debouncedFilters || {},
          pagination: {
            limit: pagination?.limit || 20,
            offset: localInfluencers.length
          }
        },
        updateQuery: (previousResult, { fetchMoreResult }) => {
          if (!fetchMoreResult) return previousResult;

          const newNodes = fetchMoreResult.influencers.nodes;

          const existingIds = new Set(previousResult.influencers.nodes.map((inf: any) => inf.id));
          const uniqueNewNodes = newNodes.filter((inf: any) => !existingIds.has(inf.id));

          return {
            influencers: {
              ...fetchMoreResult.influencers,
              nodes: [...previousResult.influencers.nodes, ...uniqueNewNodes],
              totalCount: fetchMoreResult.influencers.totalCount,
              hasNextPage: fetchMoreResult.influencers.hasNextPage
            }
          };
        }
      });

    } catch (error) {
      // Log removido para reduzir volume de console
      toast.error('Erro ao carregar mais influenciadores');
    } finally {
      setIsLoadingMore(false);
    }
  }, [
    hasMoreData, 
    isLoadingMore, 
    influencersLoading, 
    fetchMore, 
    localInfluencers.length, 
    userId, 
    debouncedFilters, 
    pagination
  ]);

  // 🚫 QUERY DESABILITADA: Não executar query de cache stats automaticamente
  const { data: cacheStatsData } = useQuery(GET_CACHE_STATS, {
    skip: true, // 🚫 DESABILITADO: Não executar esta query
    errorPolicy: 'ignore',
    fetchPolicy: 'cache-first',
    notifyOnNetworkStatusChange: false
  });

  // ===== MUTATIONS =====

  const [createInfluencerMutation] = useMutation(CREATE_INFLUENCER, {
    onError: (error) => {
      toast.error('Erro ao criar influenciador via GraphQL');
    }
  });

  const [updateInfluencerMutation] = useMutation(UPDATE_INFLUENCER, {
    onError: (error) => {
      toast.error('Erro ao atualizar influenciador via GraphQL');
    }
  });

  const [deleteInfluencerMutation] = useMutation(DELETE_INFLUENCER, {
    onError: (error) => {
      
      toast.error('Erro ao deletar influenciador via GraphQL');
    }
  });

  // ===== FUNÇÕES AUXILIARES =====

  const apolloClient = useApolloClient();

  const fetchInfluencerById = useCallback(async (id: string) => {
    try {
      if (!userId) {
        throw new Error('userId é obrigatório para buscar influenciador');
      }

      const result = await apolloClient.query({
        query: GET_INFLUENCER_BY_ID,
        variables: { id, userId },
        fetchPolicy: 'network-only'
      });
      
      const influencerData = result.data?.influencer;
      
      if (influencerData) {

        setSelectedInfluencer(influencerData);
        return influencerData;
      }

      return null;
    } catch (error) {
      
      toast.error('Erro ao buscar detalhes do influenciador');
      throw error;
    }
  }, [userId, apolloClient]);

  const createInfluencer = useCallback(async (input: any) => {
    try {
      // 🧹 FILTRO DE LIMPEZA: Remover campos técnicos antes de enviar
      const cleanInput = { ...input };
      
      const technicalFields = ['id', '__typename', 'createdAt', 'updatedAt', 'currentPricing', 'currentDemographics'];
      technicalFields.forEach(field => {
        if (cleanInput.hasOwnProperty(field)) {
          console.log(`🧹 [useInfluencersGraphQL] Removendo campo técnico: ${field}`);
          delete cleanInput[field];
        }
      });

      // Limpar __typename dos arrays audienceLocations e audienceCities
      if (cleanInput.audienceLocations && Array.isArray(cleanInput.audienceLocations)) {
        console.log(`🧹 [useInfluencersGraphQL] Limpando __typename de audienceLocations`);
        cleanInput.audienceLocations = cleanInput.audienceLocations.map((item: any) => {
          const cleanItem = { ...item };
          delete cleanItem.__typename;
          return cleanItem;
        });
      }
      
      if (cleanInput.audienceCities && Array.isArray(cleanInput.audienceCities)) {
        console.log(`🧹 [useInfluencersGraphQL] Limpando __typename de audienceCities`);
        cleanInput.audienceCities = cleanInput.audienceCities.map((item: any) => {
          const cleanItem = { ...item };
          delete cleanItem.__typename;
          return cleanItem;
        });
      }

      console.log('🚀 [useInfluencersGraphQL] Input limpo para criação:', cleanInput);

      const result = await createInfluencerMutation({
        variables: { input: cleanInput }
      });

      // 🔥 CORREÇÃO: Atualizar estado local após sucesso
      if (result.data?.createInfluencer) {
        toast.success('Influenciador criado com sucesso via GraphQL!');
        setLocalInfluencers(prev => {
          const exists = prev.some((inf: any) => inf.id === result.data.createInfluencer.id);
          if (!exists) {
            return [result.data.createInfluencer, ...prev];
          }
          return prev;
        });
      }

      return result.data?.createInfluencer;
    } catch (error) {
      console.error('❌ [useInfluencersGraphQL] Erro ao criar influenciador:', error);
      throw error;
    }
  }, [createInfluencerMutation]);

  const updateInfluencer = useCallback(async (id: string, input: any) => {
    try {
      const cleanInput = { ...input };
      
      const technicalFields = ['id', '__typename', 'createdAt', 'updatedAt', 'currentPricing', 'currentDemographics'];
      technicalFields.forEach(field => {
        if (cleanInput.hasOwnProperty(field)) {
          
          delete cleanInput[field];
        }
      });

      if (cleanInput.audienceLocations && Array.isArray(cleanInput.audienceLocations)) {
        
        cleanInput.audienceLocations = cleanInput.audienceLocations.map((item: any) => {
          const cleanItem = { ...item };
          
          delete cleanItem.__typename;
          
          return cleanItem;
        });
      }
      
      if (cleanInput.audienceCities && Array.isArray(cleanInput.audienceCities)) {
        cleanInput.audienceCities = cleanInput.audienceCities.map((item: any) => {
          const cleanItem = { ...item };
          delete cleanItem.__typename;
          
          return cleanItem;
        });
      }

      const result = await updateInfluencerMutation({
        variables: { id, input: cleanInput }
      });

      // 🔥 CORREÇÃO: Atualizar estado local após sucesso
      if (result.data?.updateInfluencer) {
        toast.success('Influenciador atualizado com sucesso via GraphQL!');
        setLocalInfluencers(prev => prev.map((inf: any) => 
          inf.id === result.data.updateInfluencer.id ? { ...inf, ...result.data.updateInfluencer } : inf
        ));
      }

      return result.data?.updateInfluencer;
    } catch (error) {
      
      throw error;
    }
  }, [updateInfluencerMutation]);

  const deleteInfluencer = useCallback(async (id: string) => {
    try {

      const result = await deleteInfluencerMutation({
        variables: { id }
      });

      // 🔥 CORREÇÃO: Atualizar estado local após sucesso
      if (result.data?.deleteInfluencer) {
        toast.success('Influenciador deletado com sucesso via GraphQL!');
        setLocalInfluencers(prev => prev.filter((inf: any) => inf.id !== id));
      }

      return result.data?.deleteInfluencer;
    } catch (error) {
      
      throw error;
    }
  }, [deleteInfluencerMutation]);

  const refreshData = useCallback(async () => {
    
    try {
      setLocalInfluencers([]);
      setHasMoreData(true);
      await refetchInfluencers();
    } catch (error) {
      
      toast.error('Erro ao atualizar dados');
    }
  }, [refetchInfluencers]);

  // 🔥 CORREÇÃO: Memoizar os influenciadores para evitar re-renders desnecessários
  const memoizedInfluencers = useMemo(() => {
    const uniqueInfluencers = localInfluencers.reduce((acc, current) => {
      const existingInfluencer = acc.find((inf: any) => inf.id === current.id);
      if (!existingInfluencer) {
        acc.push(current);
      }
      return acc;
    }, [] as any[]);
    
    if (localInfluencers.length !== uniqueInfluencers.length) {
      
    }
    
    return uniqueInfluencers;
  }, [localInfluencers]);

  // ===== RETORNO DO HOOK =====

  return {
    influencers: memoizedInfluencers,
    totalCount: influencersData?.influencers?.totalCount || 0,
    hasNextPage: hasMoreData,
    hasPreviousPage: influencersData?.influencers?.hasPreviousPage || false,
    selectedInfluencer,
    cacheStats: cacheStatsData?.cacheStats,
    
    loading: influencersLoading,
    loadingMore: isLoadingMore,
    error: influencersError,
    
    fetchInfluencerById,
    createInfluencer,
    updateInfluencer,
    deleteInfluencer,
    refreshData,
    setSelectedInfluencer,
    loadMore,
    
    refetch: refetchInfluencers
  };
}

export default useInfluencersGraphQL; 

