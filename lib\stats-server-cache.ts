import { unstable_cache } from 'next/cache'
import { db } from '@/lib/firebase-admin'

// Tipos para estatísticas otimizadas
interface BaseStats {
  totalInfluencers: number;
  totalViews: number;
  totalFollowers: number;
  totalBrands: number;
}

// 🚀 OTIMIZAÇÃO: Cache do Next.js com revalidação inteligente
export const getCachedUserStats = unstable_cache(
  async (userId: string): Promise<BaseStats> => {
    try {
      console.log('📊 [ServerCache] Calculando estatísticas para:', userId);
      
      // Buscar contador denormalizado primeiro
      const userStatsDoc = await db.collection('user_stats').doc(userId).get();
      
      if (userStatsDoc.exists) {
        const data = userStatsDoc.data();
        const lastUpdated = data?.lastUpdated?.toDate();
        const now = new Date();
        const diffMinutes = lastUpdated ? (now.getTime() - lastUpdated.getTime()) / (1000 * 60) : Infinity;
        
        // Se cache é válido (menos de 60 minutos), usar dados denormalizados
        if (diffMinutes < 60 && data) {
          console.log('✅ [ServerCache] Usando dados denormalizados válidos');
          return {
            totalInfluencers: validateNumber(data.totalInfluencers),
            totalViews: validateNumber(data.totalViews),
            totalFollowers: validateNumber(data.totalFollowers),
            totalBrands: validateNumber(data.totalBrands)
          };
        }
      }
      
      // Fallback: calcular diretamente
      console.log('🔄 [ServerCache] Calculando a partir das coleções...');
      return await calculateStatsFromCollections(userId);
      
    } catch (error) {
      console.error('❌ [ServerCache] Erro ao buscar estatísticas:', error);
      return getDefaultStats();
    }
  },
  ['user-stats'], // Cache key
  {
    revalidate: 1800, // 30 minutos
    tags: ['user-stats']
  }
);

// Função para revalidar cache quando dados mudam
export async function revalidateUserStats(userId: string) {
  const { revalidateTag } = await import('next/cache');
  revalidateTag('user-stats');
  console.log('🔄 [ServerCache] Cache revalidado para:', userId);
}

// Função para validar números
function validateNumber(value: any): number {
  if (typeof value === 'number' && !isNaN(value) && value >= 0) {
    return value;
  }
  if (typeof value === 'string' && !isNaN(Number(value))) {
    const num = Number(value);
    return num >= 0 ? num : 0;
  }
  return 0;
}

// Calcular estatísticas das coleções
async function calculateStatsFromCollections(userId: string): Promise<BaseStats> {
  try {
    const [influencersQuery, brandsQuery] = await Promise.all([
      db.collection('influencers')
        .where('userId', '==', userId)
        .get(),
      db.collection('brands')
        .where('userId', '==', userId)
        .get()
    ]);

    let totalFollowers = 0;
    let totalViews = 0;

    influencersQuery.forEach(doc => {
      const data = doc.data();
      
      totalFollowers += parseFollowersCount(data.instagramFollowers) +
                       parseFollowersCount(data.tiktokFollowers) +
                       parseFollowersCount(data.youtubeFollowers);
      
      totalViews += parseFollowersCount(data.instagramViews) +
                   parseFollowersCount(data.tiktokViews) +
                   parseFollowersCount(data.youtubeViews);
    });
    
    const stats: BaseStats = {
      totalInfluencers: influencersQuery.size,
      totalViews: totalViews,
      totalFollowers: totalFollowers,
      totalBrands: brandsQuery.size
    };

    // Salvar no contador para cache futuro
    await saveStatsCounter(userId, stats);
    
    return stats;
    
  } catch (error) {
    console.error('❌ [ServerCache] Erro ao calcular estatísticas:', error);
    return getDefaultStats();
  }
}

// Parse de contadores
function parseFollowersCount(value: any): number {
  if (!value) return 0;
  
  if (typeof value === 'number') {
    return value;
  }
  
  if (typeof value === 'string') {
    const cleanValue = value.toString().trim().toLowerCase();
    const numericPart = cleanValue.replace(/[^\d.,kmb]/g, '');
    
    if (numericPart.includes('b')) {
      return Math.floor(parseFloat(numericPart.replace('b', '')) * 1000000000);
    } else if (numericPart.includes('m')) {
      return Math.floor(parseFloat(numericPart.replace('m', '')) * 1000000);
    } else if (numericPart.includes('k')) {
      return Math.floor(parseFloat(numericPart.replace('k', '')) * 1000);
    } else {
      const parsed = parseFloat(numericPart.replace(',', '.'));
      return isNaN(parsed) ? 0 : Math.floor(parsed);
    }
  }
  
  return 0;
}

// Salvar contador
async function saveStatsCounter(userId: string, stats: BaseStats): Promise<void> {
  try {
    await db.collection('user_stats').doc(userId).set({
      ...stats,
      lastUpdated: new Date()
    }, { merge: true });
    
    console.log('✅ [ServerCache] Contador salvo');
  } catch (error) {
    console.error('❌ [ServerCache] Erro ao salvar contador:', error);
  }
}

// Estatísticas padrão
function getDefaultStats(): BaseStats {
  return {
    totalInfluencers: 0,
    totalViews: 0,
    totalFollowers: 0,
    totalBrands: 0
  };
}

// API para invalidar cache via webhook/API
export async function invalidateStatsCache(userId: string) {
  try {
    // Remover dados denormalizados
    await db.collection('user_stats').doc(userId).delete();
    
    // Revalidar cache do Next.js
    await revalidateUserStats(userId);
    
    console.log('✅ [ServerCache] Cache invalidado para:', userId);
    return true;
  } catch (error) {
    console.error('❌ [ServerCache] Erro ao invalidar cache:', error);
    return false;
  }
}
