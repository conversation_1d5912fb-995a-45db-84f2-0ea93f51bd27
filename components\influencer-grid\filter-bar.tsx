"use client"

import React, { useState } from "react"
import { Search, Filter, X, Building2, Check } from "lucide-react"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Protect } from "@clerk/nextjs"
import { useCategories } from "@/hooks/use-graphql-influencers"
import { useBrandsList } from "@/hooks/use-brands"
import { useTranslations } from "@/hooks/use-translations"
import { useUser } from "@clerk/nextjs"

interface FilterBarProps {
  searchTerm: string
  selectedCategory: string
  onSearchChange: (value: string) => void
  onCategoryChange: (category: string) => void
  onBrandFilterChange?: (selectedBrands: string[]) => void
}

// Componente da barra de pesquisa
function SearchInput({ searchTerm, onSearchChange, placeholder }: {
  searchTerm: string
  onSearchChange: (value: string) => void
  placeholder: string
}) {
  return (
    <div className="relative group">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground group-focus-within:text-[#ff0074] transition-colors duration-200" />
        <input 
          type="text" 
        className="w-full pl-10 pr-10 py-3 border border-border bg-background text-foreground rounded-xl 
                   focus:outline-none focus:ring-2 focus:ring-[#ff0074]/20 focus:border-[#ff0074] 
                   placeholder:text-muted-foreground transition-all duration-200
                   hover:border-[#ff0074]/50 shadow-sm hover:shadow-md" 
        placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      {searchTerm && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted rounded-full"
          onClick={() => onSearchChange('')}
        >
          <X className="w-3 h-3" />
        </Button>
      )}
    </div>
  )
}

// Componente do seletor de categoria
function CategorySelector({ selectedCategory, onCategoryChange, categories, error }: {
  selectedCategory: string
  onCategoryChange: (category: string) => void
  categories: Array<{ id: string; name: string; slug: string }>
  error: any
}) {
  const selectedCategoryData = categories.find((cat) => cat.id === selectedCategory)
  
  return (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
       
      </div>

      {error ? (
        <Badge className="bg-destructive/10 text-destructive border-destructive/20 text-xs px-3 py-1.5">
          Erro ao carregar categorias
        </Badge>
      ) : (
        <Select value={selectedCategory} onValueChange={onCategoryChange}>
          <SelectTrigger className="w-40 h-9 text-sm border-border bg-background 
                                   hover:border-[#ff0074]/50 focus:ring-2 focus:ring-[#ff0074]/20 
                                   focus:border-[#ff0074] transition-all duration-200 rounded-lg shadow-sm hover:shadow-md">
            <div className="flex items-center gap-2 w-full">
              {selectedCategory !== 'all' && selectedCategoryData && (
                <div className="w-2 h-2 bg-[#ff0074] rounded-full flex-shrink-0" />
              )}
              <SelectValue 
                placeholder="Todas as categorias"
                className="text-left truncate"
              >
                {selectedCategoryData?.name || "Todas as categorias"}
              </SelectValue>
            </div>
          </SelectTrigger>
          
          <SelectContent className="bg-popover border-border shadow-xl rounded-xl max-h-80 overflow-hidden">
            <div className="p-2">
              {categories.map((category) => (
                <SelectItem 
                  key={category.id} 
                  value={category.id}
                  className="text-sm cursor-pointer focus:bg-[#ff0074]/10 focus:text-[#ff0074] 
                           hover:bg-accent hover:text-accent-foreground transition-colors duration-150
                           rounded-lg my-1 px-3 py-2"
                >
                  <div className="flex items-center justify-between w-full gap-3">
                    <div className="flex items-center gap-2">
                      {category.id !== 'all' && (
                        <div className="w-2 h-2 bg-[#5600ce] rounded-full flex-shrink-0" />
                      )}
                      <span className="truncate font-medium">{category.name}</span>
                    </div>
                    
                   
                  </div>
                </SelectItem>
              ))}
            </div>
          </SelectContent>
        </Select>
      )}
    </div>
  )
}

// Componente do seletor de marcas
function BrandSelector({ onBrandFilterChange }: {
  onBrandFilterChange?: (selectedBrands: string[]) => void
}) {
  const { brands, loading, error } = useBrandsList()
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  const toggleBrand = (brandId: string) => {
    const newSelection = selectedBrands.includes(brandId)
      ? selectedBrands.filter(id => id !== brandId)
      : [...selectedBrands, brandId]
    
    setSelectedBrands(newSelection)
    onBrandFilterChange?.(newSelection)
  }

  const clearBrands = () => {
    setSelectedBrands([])
    onBrandFilterChange?.([])
  }

  const filteredBrands = brands.filter(brand => 
    brand.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Protect role="org:admin">
             <div className="flex items-center gap-3">
         <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
           <Building2 className="w-4 h-4" />
         </div>
        
        {error ? (
          <Badge className="bg-destructive/10 text-destructive border-destructive/20 text-xs px-3 py-1.5">
            Erro ao carregar marcas
          </Badge>
        ) : (
          <Popover>
            <PopoverTrigger asChild>
                           <Button
                 className="w-40 h-9 justify-between text-sm border-border bg-background 
                           hover:border-[#5600ce]/50 focus:ring-2 focus:ring-[#5600ce]/20 
                           focus:border-[#5600ce] transition-all duration-200 rounded-lg shadow-sm hover:shadow-md"
              >
                <div className="flex items-center gap-2">
                  {selectedBrands.length > 0 && (
                    <div className="w-2 h-2 bg-[#5600ce] rounded-full flex-shrink-0" />
                  )}
                  <span className="truncate">
                    {selectedBrands.length === 0 
                      ? "Todas as marcas" 
                      : selectedBrands.length === 1 
                        ? brands.find(b => b.id === selectedBrands[0])?.name || "Marca selecionada"
                        : `${selectedBrands.length} marcas selecionadas`
                    }
                  </span>
                </div>
                <Filter className="w-4 h-4 text-muted-foreground" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
              <Card className="border-0 shadow-lg">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-sm">Selecionar Marcas</h4>
                    {selectedBrands.length > 0 && (
            <Button 
              size="sm"
                        className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
                        onClick={clearBrands}
                      >
                        Limpar
                      </Button>
                    )}
                  </div>
                  
                  <Input
                    placeholder="Buscar marcas..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="mb-3 h-8"
                  />
                  
                  {loading ? (
                    <div className="text-sm text-muted-foreground py-4 text-center">
                      Carregando marcas...
                    </div>
                  ) : filteredBrands.length === 0 ? (
                    <div className="text-sm text-muted-foreground py-4 text-center">
                      {searchTerm ? "Nenhuma marca encontrada" : "Nenhuma marca disponível"}
                    </div>
                  ) : (
                    <ScrollArea className="h-60">
                      <div className="space-y-1">
                        {filteredBrands.map((brand) => (
                          <Button
                            key={brand.id}
                            className="w-full justify-start h-auto p-3 hover:bg-muted/50 bg-transparent text-left"
                            onClick={() => toggleBrand(brand.id)}
                          >
                            <div className="flex items-center gap-3 w-full">
                              <div className="flex items-center justify-center w-4 h-4">
                                {selectedBrands.includes(brand.id) && (
                                  <Check className="w-3 h-3 text-[#5600ce]" />
                                )}
                              </div>
                              
                              {brand.logo && (
                                <img 
                                  src={brand.logo} 
                                  alt={brand.name}
                                  className="w-6 h-6 rounded object-cover"
                                />
                              )}
                              
                              <div className="text-left flex-1">
                                <div className="font-medium text-sm text-foreground">
                                  {brand.name}
                                </div>
                              </div>
                            </div>
            </Button>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              </Card>
            </PopoverContent>
          </Popover>
        )}
      </div>
    </Protect>
  )
}

// Componente de filtros ativos compacto
function ActiveFilters({ searchTerm, selectedCategory, categories, onSearchChange, onCategoryChange }: {
  searchTerm: string
  selectedCategory: string
  categories: Array<{ id: string; name: string; slug: string }>
  onSearchChange: (value: string) => void
  onCategoryChange: (category: string) => void
}) {
  const hasActiveFilters = searchTerm || selectedCategory !== 'all'
  
  if (!hasActiveFilters) return <div className="text-xs text-muted-foreground whitespace-nowrap">Sem filtros</div>
  
  const activeFiltersCount = (searchTerm ? 1 : 0) + (selectedCategory !== 'all' ? 1 : 0)
  
  return (
    <div className="flex items-center gap-2 whitespace-nowrap">
      <Badge className="bg-muted/50 text-muted-foreground border-border text-xs px-2 py-1 gap-1 flex items-center">
        {activeFiltersCount} filtro{activeFiltersCount > 1 ? 's' : ''}
        <Button
          className="h-3 w-3 p-0 hover:bg-muted rounded-full ml-1 flex items-center justify-center"
          onClick={() => {
            onSearchChange('')
            onCategoryChange('all')
          }}
          title="Limpar todos os filtros"
        >
          <X className="w-2 h-2" />
        </Button>
      </Badge>
    </div>
  )
}

export function FilterBar({ 
  searchTerm, 
  selectedCategory, 
  onSearchChange, 
  onCategoryChange,
  onBrandFilterChange
}: FilterBarProps) {
  const { t } = useTranslations()
  const { user, isLoaded } = useUser()
  
  console.log('🔍 [FilterBar] Clerk user:', { userId: user?.id, isLoaded, user });
  
  const { categories, error } = useCategories(user?.id) // 🔄 CORREÇÃO: Passar userId para incluir categorias do usuário

  return (
    <Card className="px-4 py-2.5 border-border/50 shadow-sm bg-background/50 backdrop-blur-sm">
      {/* Filtros em uma única linha compacta */}
      <div className="flex flex-col text-sm lg:flex-row gap-2 items-start lg:items-center">
        {/* Barra de pesquisa */}
        <div className="flex-1 text-sm w-full lg:max-w-sm">
          <SearchInput
            searchTerm={searchTerm}
            onSearchChange={onSearchChange}
            placeholder={String(t('influencers.search_placeholder') || "Pesquisar influenciador...")}
          />
        </div>
        
       
        
        {/* Seletores de filtro */}
        <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
          <CategorySelector
            selectedCategory={selectedCategory}
            onCategoryChange={onCategoryChange}
            categories={categories}
            error={error}
          />
          
          <BrandSelector 
            onBrandFilterChange={onBrandFilterChange}
          />
        </div>
        
       
        
        {/* Filtros ativos inline */}
        <div className="flex-shrink-0">
          <ActiveFilters
            searchTerm={searchTerm}
            selectedCategory={selectedCategory}
            categories={categories}
            onSearchChange={onSearchChange}
            onCategoryChange={onCategoryChange}
          />
        </div>
      </div>
    </Card>
  )
}



