import { Suspense } from 'react'
import { StatsProvider } from './stats-provider'
import { AnimatedStats } from './animated-stats'

// Tipos para dados de influencers
interface InfluencerData {
  id: string;
  instagramFollowers?: number;
  tiktokFollowers?: number;
  youtubeFollowers?: number;
  instagramViews?: number;
  tiktokViews?: number;
  youtubeViews?: number;
}

interface OptimizedStatsProps {
  userId: string;
  filteredInfluencers?: InfluencerData[];
  influencersCount: number;
  // Props para FilterBar
  searchTerm?: string;
  selectedCategory?: string;
  onSearchChange?: (value: string) => void;
  onCategoryChange?: (category: string) => void;
  // Props para Toolbar
  viewMode?: "grid" | "list";
  selectionMode?: boolean;
  selectedCount?: number;
  onViewModeChange?: (mode: "grid" | "list") => void;
  onAddInfluencer?: () => void;
  onToggleSelectionMode?: () => void;
  onDeleteSelected?: () => void;
  onDuplicateSelected?: () => void;
  onBrandFilterChange?: (selectedBrands: string[]) => void;
  verifiedOnly?: boolean;
  onVerifiedOnlyChange?: (value: boolean) => void;
  onSendToBrand?: (brandId: string, brandName: string) => void;
}

// Componente de loading otimizado
function StatsLoadingSkeleton() {
  return (
    <div className="sticky top-0 z-5 w-full bg-background backdrop-blur-sm border-b border-border/40">
      <div className="px-2 py-4 space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-6">
            {/* Skeleton para estatística de Influencers */}
            <div className="flex items-center">
              <div className="w-5 h-5 mr-2 bg-muted-foreground/20 rounded animate-pulse"></div>
              <div className="w-8 h-4 bg-muted-foreground/20 rounded animate-pulse mr-1"></div>
              <div className="w-20 h-3 bg-muted-foreground/20 rounded animate-pulse"></div>
            </div>
            
            <div className="text-muted-foreground">|</div>
            
            {/* Skeleton para estatística de Views */}
            <div className="flex items-center">
              <div className="w-5 h-5 mr-2 bg-muted-foreground/20 rounded animate-pulse"></div>
              <div className="w-12 h-4 bg-muted-foreground/20 rounded animate-pulse mr-1"></div>
              <div className="w-16 h-3 bg-muted-foreground/20 rounded animate-pulse"></div>
            </div>
            
            <div className="text-muted-foreground">|</div>
            
            {/* Skeleton para estatística de Followers */}
            <div className="flex items-center">
              <div className="w-5 h-5 mr-2 bg-muted-foreground/20 rounded animate-pulse"></div>
              <div className="w-12 h-4 bg-muted-foreground/20 rounded animate-pulse mr-1"></div>
              <div className="w-20 h-3 bg-muted-foreground/20 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 🚀 COMPONENTE OTIMIZADO: Usa Server Components + cache do React
export function OptimizedStats(props: OptimizedStatsProps) {
  const { userId, ...restProps } = props;

  return (
    <Suspense fallback={<StatsLoadingSkeleton />}>
      <StatsProvider userId={userId}>
        {(baseStats) => (
          <AnimatedStats
            baseStats={baseStats}
            {...restProps}
          />
        )}
      </StatsProvider>
    </Suspense>
  );
}

// 🚀 VERSÃO AINDA MAIS OTIMIZADA: Para uso com Next.js cache
export async function ServerOptimizedStats(props: OptimizedStatsProps) {
  const { userId, ...restProps } = props;

  return (
    <StatsProvider userId={userId}>
      {(baseStats) => (
        <AnimatedStats
          baseStats={baseStats}
          {...restProps}
        />
      )}
    </StatsProvider>
  );
}

// Hook para migração gradual (compatibilidade com código existente)
export function useOptimizedStats(userId: string) {
  console.warn('⚠️ [OptimizedStats] useOptimizedStats é deprecated. Use OptimizedStats component diretamente.');
  
  return {
    stats: { totalInfluencers: 0, totalViews: 0, totalFollowers: 0, totalBrands: 0 },
    loading: false,
    error: null,
    refetch: () => Promise.resolve()
  };
}
