import { NextRequest, NextResponse } from 'next/server';
import { Budget, CreateBudgetInput, UpdateBudgetInput, BudgetFilters } from '../../../types/budget';
import { db } from '../../../lib/firebase-admin';
import { ProposalService } from '../../../services/proposal-service';

// Função auxiliar para obter preço original do GraphQL/currentPricing
async function getOriginalPriceFromGraphQL(influencerId: string, serviceType: string): Promise<number> {
  try {
    // Buscar dados do influenciador
    const influencerDoc = await db.collection('influencers').doc(influencerId).get();
    const influencerData = influencerDoc.data();
    
    if (!influencerData) return 0;
    
    // Mapear serviceType para estrutura de dados financeiros
    const serviceMapping: { [key: string]: string } = {
      // Formatos com underscore para camelCase
      'instagram_story': 'instagramStory',
      'instagram_reel': 'instagramReel',
      'instagram_post': 'instagramPost',
      'youtube_video': 'youtubeDedicated',
      'youtube_dedicated': 'youtubeDedicated',
      'youtube_shorts': 'youtubeShorts',
      'youtube_insertion': 'youtubeInsertion',
      'tiktok_video': 'tiktokVideo',
      'facebook_post': 'facebookPost',
      'twitch_stream': 'twitchStream',
      'kwai_video': 'kwaiVideo',
      // Formatos já em camelCase (passthrough)
      'instagramStory': 'instagramStory',
      'instagramReel': 'instagramReel',
      'instagramPost': 'instagramPost',
      'youtubeDedicated': 'youtubeDedicated',
      'youtubeShorts': 'youtubeShorts',
      'youtubeInsertion': 'youtubeInsertion',
      'tiktokVideo': 'tiktokVideo',
      'facebookPost': 'facebookPost',
      'twitchStream': 'twitchStream',
      'kwaiVideo': 'kwaiVideo'
    };
    
    const mappedService = serviceMapping[serviceType] || serviceType;
    
    // Tentar buscar preço dos dados financeiros
    const financialData = influencerData.dadosFinanceiros;
    if (financialData?.precos?.[mappedService]?.price) {
      return financialData.precos[mappedService].price;
    }
    
    return 0;
  } catch (error) {
    console.error('Erro ao buscar preço original:', error);
    return 0;
  }
}

export async function POST(request: NextRequest) {
  try {
    const budgetData: CreateBudgetInput = await request.json();

    // Validar dados obrigatórios (sem exigir proposalId)
    if (!budgetData.influencerId || !budgetData.userId || !budgetData.amount || !budgetData.brandId) {
      return NextResponse.json(
        { error: 'Campos obrigatórios: influencerId, userId, brandId e amount' },
        { status: 400 }
      );
    }

    // Preparar dados do orçamento
    const budget: any = {
      influencerId: budgetData.influencerId,
      influencerName: budgetData.influencerName || '',
      userId: budgetData.userId,
      brandId: budgetData.brandId,                          
      amount: budgetData.amount,
      currency: budgetData.currency || 'BRL',
      description: budgetData.description || '',
      serviceType: budgetData.serviceType || 'personalizado',
      status: 'draft',                                      // Sempre inicia como draft
      counterProposals: [],                                 // 🆕 Array para contrapropostas
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: budgetData.userId
    };

    // Incluir campos opcionais se não forem undefined
    if (budgetData.services !== undefined) {
      budget.services = budgetData.services;
    }
    
    if (budgetData.expiresAt !== undefined) {
      budget.expiresAt = budgetData.expiresAt;
    }

    // ✅ ESTRUTURA HIERÁRQUICA: proposals/{proposalId}/influencers/{influencerId}/budgets/{budgetId}
    if (budgetData.proposalId) {
      console.log('🏢 [POST] Criando orçamento na subcoleção hierárquica:', {
        proposalId: budgetData.proposalId,
        influencerId: budgetData.influencerId,
        serviceType: budgetData.serviceType
      });

      console.log('✅ [POST] Processando criação de orçamento');

      // Preparar dados completos do orçamento
      const budgetDocument = {
        influencerId: budgetData.influencerId,
        influencerName: budgetData.influencerName,
        userId: budgetData.userId,
        brandId: budgetData.brandId,
        proposalId: budgetData.proposalId,
        amount: budgetData.amount,
        originalPrice: await getOriginalPriceFromGraphQL(budgetData.influencerId, budgetData.serviceType || 'personalizado'),
        currency: budgetData.currency || 'BRL',
        description: budgetData.description || `Orçamento para ${budgetData.serviceType}`,
        serviceType: budgetData.serviceType || 'personalizado',
        status: 'draft',
        hasCounterProposal: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: budgetData.userId
      };

      console.log('📄 [POST] Estrutura do orçamento:', budgetDocument);

      // 🔥 CORREÇÃO: Verificar se já existe budget com mesmo serviceType para evitar duplicatas
      const existingBudgetsSnapshot = await db
        .collection('proposals')
        .doc(budgetData.proposalId)
        .collection('influencers')
        .doc(budgetData.influencerId)
        .collection('budgets')
        .where('serviceType', '==', budgetData.serviceType)
        .orderBy('updatedAt', 'desc')
        .limit(1)
        .get();

      let budgetRef;
      
      if (!existingBudgetsSnapshot.empty) {
        // ✅ ATUALIZAR: Budget existente encontrado, atualizar ao invés de criar novo
        const existingBudgetDoc = existingBudgetsSnapshot.docs[0];
        console.log('🔄 [POST] Budget existente encontrado, atualizando:', {
          existingId: existingBudgetDoc.id,
          serviceType: budgetData.serviceType,
          oldAmount: existingBudgetDoc.data().amount,
          newAmount: budgetDocument.amount
        });
        
        budgetRef = existingBudgetDoc.ref;
        await budgetRef.update({
          ...budgetDocument,
          updatedAt: new Date()
        });
        
        console.log('✅ [POST] Budget atualizado com ID:', existingBudgetDoc.id);
      } else {
        // ✅ CRIAR: Nenhum budget existente, criar novo
        console.log('🆕 [POST] Criando novo budget para serviceType:', budgetData.serviceType);
        
        budgetRef = await db
          .collection('proposals')
          .doc(budgetData.proposalId)
          .collection('influencers')
          .doc(budgetData.influencerId)
          .collection('budgets')
          .add(budgetDocument);
          
        console.log('✅ [POST] Novo budget criado com ID:', budgetRef.id);
      }

      const finalBudgetId = budgetRef.id || existingBudgetsSnapshot.docs[0]?.id;
      console.log('✅ [POST] Orçamento processado com ID:', finalBudgetId);

      // 🔥 CORREÇÃO: Recalcular total da proposta SEMPRE após criar/atualizar orçamento
      try {
        console.log('💰 [POST] Recalculando total da proposta:', budgetData.proposalId);
        
        // Usar o método forcado para garantir que seja executado
        const recalcResult = await ProposalService.forceRecalculateProposalTotal(budgetData.proposalId);
        
        if (recalcResult.success) {
          console.log('✅ [POST] Total da proposta recalculado:', recalcResult.message);
        } else {
          console.warn('⚠️ [POST] Erro no recálculo:', recalcResult.message);
        }
      } catch (recalcError) {
        console.warn('⚠️ [POST] Erro crítico ao recalcular total da proposta:', recalcError);
      }

      return NextResponse.json({
        success: true,
        budgetId: finalBudgetId,
        budget: { 
          id: finalBudgetId, 
          ...budgetDocument
        },
        message: !existingBudgetsSnapshot.empty ? 'Orçamento atualizado na subcoleção hierárquica!' : 'Orçamento criado na subcoleção hierárquica!'
      });
    } 
    // Senão, salvar como orçamento independente na subcoleção do influenciador
    else {
      console.log('🏢 [POST] Criando orçamento independente para influencer:', {
        influencerId: budgetData.influencerId,
        serviceType: budgetData.serviceType
      });

      // Determinar a plataforma baseada no serviceType
      let platform = 'outros';
      if (budgetData.serviceType?.includes('instagram')) {
        platform = 'instagram';
      } else if (budgetData.serviceType?.includes('tiktok')) {
        platform = 'tiktok';
      } else if (budgetData.serviceType?.includes('youtube')) {
        platform = 'youtube';
      } else if (budgetData.serviceType?.includes('facebook')) {
        platform = 'facebook';
      } else if (budgetData.serviceType?.includes('twitch')) {
        platform = 'twitch';
      } else if (budgetData.serviceType?.includes('kwai')) {
        platform = 'kwai';
      }

      // NOVA ESTRUTURA: influencers/{influencerId}/budgets/{platform}
      const docRef = db
        .collection('influencers')
        .doc(budgetData.influencerId)
        .collection('budgets')
        .doc(platform);

      // Verificar se já existe um orçamento para esta plataforma
      const existingDoc = await docRef.get();
      
      if (existingDoc.exists) {
        // Atualizar orçamento existente
        await docRef.update({
          ...budget,
          updatedAt: new Date().toISOString()
        });

        console.log('✅ [POST] Orçamento atualizado para plataforma:', platform);

        return NextResponse.json({
          success: true,
          budgetId: platform,
          budget: { id: platform, ...budget },
          message: `Orçamento atualizado para ${platform}!`
        });
      } else {
        // Criar novo orçamento
        await docRef.set(budget);

        console.log('✅ [POST] Orçamento independente salvo para plataforma:', platform);

        return NextResponse.json({
          success: true,
          budgetId: platform,
          budget: { id: platform, ...budget },
          message: `Orçamento criado para ${platform}!`
        });
      }
    }

  } catch (error) {
    console.error('❌ [POST] Erro ao salvar orçamento:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const influencerId = searchParams.get('influencerId');
    const proposalId = searchParams.get('proposalId');
    const brandId = searchParams.get('brandId');
    const status = searchParams.get('status');

    console.log('📊 [GET] Buscando orçamentos:', { userId, influencerId, proposalId, brandId, status });

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    let budgets: Budget[] = [];

    if (proposalId && influencerId) {
      // Buscar orçamentos de UM influenciador em UMA proposta
      console.log('🔍 [GET] Buscando na hierarquia:', {
        proposalId, 
        influencerId
      });
      
      // VERIFICAR SE O USUÁRIO TEM ACESSO À PROPOSTA
      const proposalDoc = await db.collection('proposals').doc(proposalId).get();
      const proposalData = proposalDoc.data();
      
      if (!proposalData) {
        console.log('❌ [GET] Proposta não encontrada:', proposalId);
        return NextResponse.json({
          success: true,
          budgets: [],
          total: 0,
          error: 'Proposta não encontrada'
        });
      }
      
      // Verificar se o usuário é proprietário OU colaborador
      const isOwner = proposalData.criadoPor === userId;
      const isCollaborator = proposalData.collaborators?.some((c: any) => 
        c.userId === userId && c.status === 'active'
      );
      
      if (!isOwner && !isCollaborator) {
        console.log('❌ [GET] Usuário sem acesso à proposta:', { userId, proposalId });
        return NextResponse.json({
          success: true,
          budgets: [],
          total: 0,
          error: 'Acesso negado à proposta'
        });
      }
      
      console.log('✅ [GET] Usuário tem acesso:', { 
        userId, 
        isOwner, 
        isCollaborator,
        proposalOwner: proposalData.criadoPor 
      });
      
      // ✅ Buscar orçamentos na subcoleção hierárquica - ORDENADO POR DATA (MAIS RECENTE PRIMEIRO)
      const budgetsSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .orderBy('updatedAt', 'desc') // 🔥 CORREÇÃO: Ordenar por data de atualização, mais recente primeiro
        .get();
      
      console.log('📝 [GET] Orçamentos encontrados na subcoleção:', budgetsSnapshot.docs.length);
      
      // 🔥 DEBUG: Log detalhado dos orçamentos encontrados
      budgetsSnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`📝 [GET] Budget ${index + 1}:`, {
          id: doc.id,
          serviceType: data.serviceType,
          amount: data.amount,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt
        });
      });
      
      // Converter documentos da subcoleção para formato da API
      const allBudgets = budgetsSnapshot.docs.map(doc => {
        const data = doc.data();
                  // 🔥 CORREÇÃO: Ordenar counterProposals por data, mais recente primeiro
          const sortedCounterProposals = (data.counterProposals || []).sort((a: any, b: any) => {
            const dateA = new Date(a.proposedAt || a.createdAt || 0).getTime();
            const dateB = new Date(b.proposedAt || b.createdAt || 0).getTime();
            return dateB - dateA; // Mais recente primeiro
          });

          return {
            id: doc.id,
            proposalId: proposalId,
            influencerId: influencerId,
            influencerName: data.influencerName || '',
            userId: data.userId || '',
            brandId: data.brandId || '',
            amount: data.amount || 0,
            originalPrice: data.originalPrice || 0,
            currency: data.currency || 'BRL',
            description: data.description || '',
            serviceType: data.serviceType || 'personalizado',
            status: data.status || 'draft',
            hasCounterProposal: data.hasCounterProposal || false,
            counterProposals: sortedCounterProposals, // 🆕 Incluir counterProposals ORDENADOS
            createdAt: new Date(data.createdAt?.seconds ? data.createdAt.toDate() : data.createdAt || Date.now()),
            updatedAt: new Date(data.updatedAt?.seconds ? data.updatedAt.toDate() : data.updatedAt || Date.now()),
            createdBy: data.createdBy || data.userId
          } as Budget;
      });

      // 🔥 CORREÇÃO: Filtrar duplicatas por serviceType, mantendo apenas o mais recente
      const budgetMap = new Map<string, Budget>();
      allBudgets.forEach(budget => {
        const existing = budgetMap.get(budget.serviceType);
        if (!existing || budget.updatedAt > existing.updatedAt) {
          budgetMap.set(budget.serviceType, budget);
          console.log('💰 [DEDUP] Mantendo budget mais recente:', {
            serviceType: budget.serviceType,
            budgetId: budget.id,
            amount: budget.amount,
            updatedAt: budget.updatedAt.toISOString()
          });
        } else {
          console.log('💰 [DEDUP] Ignorando budget mais antigo:', {
            serviceType: budget.serviceType,
            budgetId: budget.id,
            amount: budget.amount,
            updatedAt: budget.updatedAt.toISOString()
          });
        }
      });
      
      budgets = Array.from(budgetMap.values());
    } else if (proposalId) {
      // TODOS OS INFLUENCIADORES DA PROPOSTA: Buscar orçamentos de todos os influenciadores
      console.log('🔍 [GET] Buscando orçamentos de TODOS os influenciadores da proposta:', proposalId);
      
      // 🔒 VERIFICAR SE O USUÁRIO TEM ACESSO À PROPOSTA
      const proposalDoc = await db.collection('proposals').doc(proposalId).get();
      const proposalData = proposalDoc.data();
      
      if (!proposalData) {
        console.log('❌ [GET] Proposta não encontrada:', proposalId);
        return NextResponse.json({
          success: true,
          budgets: [],
          total: 0,
          error: 'Proposta não encontrada'
        });
      }
      
      // Verificar se o usuário é proprietário OU colaborador
      const isOwner = proposalData.criadoPor === userId;
      const isCollaborator = proposalData.collaborators?.some((c: any) => 
        c.userId === userId && c.status === 'active'
      );
      
      if (!isOwner && !isCollaborator) {
        console.log('❌ [GET] Usuário sem acesso à proposta:', { userId, proposalId });
        return NextResponse.json({
          success: true,
          budgets: [],
          total: 0,
          error: 'Acesso negado à proposta'
        });
      }
      
      console.log('✅ [GET] Usuário tem acesso a todos os orçamentos:', { 
        userId, 
        isOwner, 
        isCollaborator,
        proposalOwner: proposalData.criadoPor 
      });
      
      // Primeiro, buscar todos os influenciadores da proposta
      const influencersSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .get();
      
      console.log('📋 [GET] Influenciadores na proposta encontrados:', influencersSnapshot.docs.length);
      
      // Para cada influenciador, buscar seus orçamentos na subcoleção
      for (const influencerDoc of influencersSnapshot.docs) {
        const budgetsSnapshot = await db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .doc(influencerDoc.id)
          .collection('budgets')
          .orderBy('updatedAt', 'desc') // 🔥 CORREÇÃO: Ordenar por data de atualização, mais recente primeiro
          .get();
        
        // Converter documentos da subcoleção para formato da API
        const allInfluencerBudgets = budgetsSnapshot.docs.map(budgetDoc => {
          const data = budgetDoc.data();
          
          // 🔥 CORREÇÃO: Ordenar counterProposals por data, mais recente primeiro
          const sortedCounterProposals = (data.counterProposals || []).sort((a: any, b: any) => {
            const dateA = new Date(a.proposedAt || a.createdAt || 0).getTime();
            const dateB = new Date(b.proposedAt || b.createdAt || 0).getTime();
            return dateB - dateA; // Mais recente primeiro
          });

          return {
            id: budgetDoc.id,
            proposalId: proposalId,
            influencerId: influencerDoc.id,
            influencerName: data.influencerName || '',
            userId: data.userId || '',
            brandId: data.brandId || '',
            amount: data.amount || 0,
            originalPrice: data.originalPrice || 0,
            currency: data.currency || 'BRL',
            description: data.description || '',
            serviceType: data.serviceType || 'personalizado',
            status: data.status || 'draft',
            hasCounterProposal: data.hasCounterProposal || false,
            counterProposals: sortedCounterProposals, // 🆕 Incluir counterProposals ORDENADOS
            createdAt: new Date(data.createdAt?.seconds ? data.createdAt.toDate() : data.createdAt || Date.now()),
            updatedAt: new Date(data.updatedAt?.seconds ? data.updatedAt.toDate() : data.updatedAt || Date.now()),
            createdBy: data.createdBy || data.userId
          } as Budget;
        });

        // 🔥 CORREÇÃO: Filtrar duplicatas por serviceType para este influenciador
        const influencerBudgetMap = new Map<string, Budget>();
        allInfluencerBudgets.forEach(budget => {
          const key = `${budget.influencerId}_${budget.serviceType}`;
          const existing = influencerBudgetMap.get(key);
          if (!existing || budget.updatedAt > existing.updatedAt) {
            influencerBudgetMap.set(key, budget);
            console.log('💰 [DEDUP_ALL] Mantendo budget mais recente:', {
              influencerId: budget.influencerId,
              serviceType: budget.serviceType,
              budgetId: budget.id,
              amount: budget.amount,
              updatedAt: budget.updatedAt.toISOString()
            });
          } else {
            console.log('💰 [DEDUP_ALL] Ignorando budget mais antigo:', {
              influencerId: budget.influencerId,
              serviceType: budget.serviceType,
              budgetId: budget.id,
              amount: budget.amount,
              updatedAt: budget.updatedAt.toISOString()
            });
          }
        });
        
        budgets = [...budgets, ...Array.from(influencerBudgetMap.values())];
      }
    } else if (influencerId) {
      // NOVO: Buscar orçamentos independentes na subcoleção do influenciador
      console.log('🔍 [GET] Buscando orçamentos independentes do influenciador:', influencerId);
      
      // Verificar se o usuário tem acesso ao influenciador
      const influencerDoc = await db.collection('influencers').doc(influencerId).get();
      const influencerData = influencerDoc.data();
      
      if (!influencerData || influencerData.userId !== userId) {
        console.log('❌ [GET] Usuário sem acesso ao influenciador:', { userId, influencerId });
        return NextResponse.json({
          success: true,
          budgets: [],
          total: 0,
          error: 'Acesso negado ao influenciador'
        });
      }
      
      // Buscar orçamentos na subcoleção do influenciador - ORDENADO POR DATA
      const budgetsSnapshot = await db
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .orderBy('updatedAt', 'desc') // 🔥 CORREÇÃO: Ordenar por data de atualização, mais recente primeiro
        .get();
      
      console.log('📝 [GET] Orçamentos independentes encontrados:', budgetsSnapshot.docs.length);
      
      const independentBudgets = budgetsSnapshot.docs.map((doc: any) => {
        const data = doc.data();
        
        // 🔥 CORREÇÃO: Ordenar counterProposals por data, mais recente primeiro
        const sortedCounterProposals = (data.counterProposals || []).sort((a: any, b: any) => {
          const dateA = new Date(a.proposedAt || a.createdAt || 0).getTime();
          const dateB = new Date(b.proposedAt || b.createdAt || 0).getTime();
          return dateB - dateA; // Mais recente primeiro
        });

        return {
          id: doc.id, // ID será o nome da plataforma (instagram, tiktok, etc)
          influencerId: influencerId,                 
          influencerName: data.influencerName || '',
          userId: data.userId || '',
          brandId: data.brandId || '',
          amount: data.amount || 0,
          currency: data.currency || 'BRL',
          description: data.description || '',
          serviceType: data.serviceType || 'personalizado',
          status: data.status || 'draft',
          hasCounterProposal: data.hasCounterProposal || false,
          counterProposals: sortedCounterProposals, // 🆕 Incluir counterProposals ORDENADOS
          createdAt: new Date(data.createdAt || Date.now()),
          updatedAt: new Date(data.updatedAt || Date.now()),
          createdBy: data.createdBy || data.userId
        } as Budget;
      });
      
      // Filtrar por marca se especificado
      if (brandId) {
        budgets = independentBudgets.filter(budget => budget.brandId === brandId);
      } else {
        budgets = independentBudgets;
      }
      
      // Filtrar por status se especificado
      if (status) {
        budgets = budgets.filter(budget => budget.status === status);
      }
    } else {
      // Buscar TODOS os orçamentos independentes do usuário
      console.log('🔍 [GET] Buscando todos os orçamentos independentes do usuário:', userId);
      
      // Buscar todos os influenciadores do usuário
      const influencersSnapshot = await db
        .collection('influencers')
        .where('userId', '==', userId)
        .get();
      
      console.log('📋 [GET] Influenciadores do usuário encontrados:', influencersSnapshot.docs.length);
      
      // Para cada influenciador, buscar seus orçamentos independentes
      for (const influencerDoc of influencersSnapshot.docs) {
        const budgetsSnapshot = await db
          .collection('influencers')
          .doc(influencerDoc.id)
          .collection('budgets')
          .orderBy('updatedAt', 'desc') // 🔥 CORREÇÃO: Ordenar por data de atualização, mais recente primeiro
          .get();
        
        const influencerBudgets = budgetsSnapshot.docs.map((doc: any) => {
          const data = doc.data();
          
          // 🔥 CORREÇÃO: Ordenar counterProposals por data, mais recente primeiro
          const sortedCounterProposals = (data.counterProposals || []).sort((a: any, b: any) => {
            const dateA = new Date(a.proposedAt || a.createdAt || 0).getTime();
            const dateB = new Date(b.proposedAt || b.createdAt || 0).getTime();
            return dateB - dateA; // Mais recente primeiro
          });

          return {
            id: doc.id, // ID será o nome da plataforma
            influencerId: influencerDoc.id,                 
            influencerName: data.influencerName || '',
            userId: data.userId || '',
            brandId: data.brandId || '',
            amount: data.amount || 0,
            currency: data.currency || 'BRL',
            description: data.description || '',
            serviceType: data.serviceType || 'personalizado',
            status: data.status || 'draft',
            hasCounterProposal: data.hasCounterProposal || false,
            counterProposals: sortedCounterProposals, // 🆕 Incluir counterProposals ORDENADOS
            createdAt: new Date(data.createdAt || Date.now()),
            updatedAt: new Date(data.updatedAt || Date.now()),
            createdBy: data.createdBy || data.userId
          } as Budget;
        });
        
        budgets = [...budgets, ...influencerBudgets];
      }
      
      // Filtrar por marca se especificado
      if (brandId) {
        budgets = budgets.filter(budget => budget.brandId === brandId);
      }
      
      // Filtrar por status se especificado
      if (status) {
        budgets = budgets.filter(budget => budget.status === status);
      }
    }

    // Ordenar orçamentos por data de criação (mais recente primeiro)
    budgets.sort((a: Budget, b: Budget) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return NextResponse.json({
      success: true,
      budgets,
      total: budgets.length
    });

  } catch (error) {
    console.error('❌ [GET] Erro ao buscar orçamentos:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { budgetId, proposalId, influencerId, userId, action, counterProposalId, quantity, ...updateData } = await request.json();

    if (!budgetId || !influencerId) {
      return NextResponse.json(
        { error: 'budgetId e influencerId são obrigatórios' },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório para validação de permissões' },
        { status: 400 }
      );
    }

    // 🆕 Tratamento específico para ações de contrapropostas
    if (action === 'accept_counter_proposal' || action === 'reject_counter_proposal') {
      if (!counterProposalId) {
        return NextResponse.json(
          { error: 'counterProposalId é obrigatório para ações de contraproposta' },
          { status: 400 }
        );
      }

      console.log(`🔄 [PUT] ${action}:`, { proposalId, influencerId, budgetId, counterProposalId });

      // Determinar referência do budget baseado na estrutura
      let budgetRef;
      if (proposalId) {
        budgetRef = db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets')
          .doc(budgetId);
      } else {
        budgetRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets')
          .doc(budgetId);
      }

      // Buscar o documento do budget
      const budgetDoc = await budgetRef.get();
      if (!budgetDoc.exists) {
        return NextResponse.json(
          { error: 'Orçamento não encontrado' },
          { status: 404 }
        );
      }

      const budgetData = budgetDoc.data();
      const counterProposals = budgetData?.counterProposals || [];

      // Atualizar o status da contraproposta específica
      const updatedCounterProposals = counterProposals.map((proposal: any) => {
        if (proposal.id === counterProposalId) {
          const newStatus = action === 'accept_counter_proposal' ? 'accepted' : 'rejected';
          return {
            ...proposal,
            status: newStatus,
            [`${newStatus}At`]: new Date().toISOString(),
            [`${newStatus}By`]: userId
          };
        }
        // 🔒 REGRA: Se estamos aceitando uma, TODAS as outras devem ser rejeitadas (independente do status atual)
        else if (action === 'accept_counter_proposal') {
          return {
            ...proposal,
            status: 'rejected',
            rejectedAt: new Date().toISOString(),
            rejectedBy: userId
          };
        }
        return proposal;
      });

      // Atualizar o documento
      await budgetRef.update({
        counterProposals: updatedCounterProposals,
        updatedAt: new Date().toISOString(),
        updatedBy: userId
      });

      console.log(`✅ [PUT] Contraproposta ${action === 'accept_counter_proposal' ? 'aceita' : 'recusada'} com sucesso`);

      return NextResponse.json({
        success: true,
        message: `Contraproposta ${action === 'accept_counter_proposal' ? 'aceita' : 'recusada'} com sucesso`
      });
    }

    // 🆕 Tratamento específico para ações de aceitar/rejeitar valor inicial
    if (action === 'accept_initial_value' || action === 'reject_initial_value') {
      console.log(`🔄 [PUT] ${action}:`, { proposalId, influencerId, budgetId, userId });

      // Determinar referência do budget baseado na estrutura
      let budgetRef;
      if (proposalId) {
        budgetRef = db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets')
          .doc(budgetId);
      } else {
        budgetRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets')
          .doc(budgetId);
      }

      // Buscar o documento do budget
      const budgetDoc = await budgetRef.get();
      if (!budgetDoc.exists) {
        return NextResponse.json(
          { error: 'Orçamento não encontrado' },
          { status: 404 }
        );
      }

      const budgetData = budgetDoc.data();
      if (!budgetData) {
        return NextResponse.json(
          { error: 'Dados do orçamento não encontrados' },
          { status: 404 }
        );
      }
      
      const counterProposals = budgetData.counterProposals || [];

      // 🆕 Criar entrada no array counterProposals para aceitar/rejeitar valor inicial
      const newStatus = action === 'accept_initial_value' ? 'accepted' : 'rejected';
      const initialValueProposal = {
        id: `initial_${newStatus}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        proposedAmount: budgetData.amount || 0,
        originalAmount: budgetData.amount || 0,
        currency: budgetData.currency || 'BRL',
        notes: `Valor inicial ${action === 'accept_initial_value' ? 'aceito' : 'rejeitado'} diretamente`,
        proposedBy: userId,
        proposedAt: new Date().toISOString(),
        status: newStatus,
        [`${newStatus}At`]: new Date().toISOString(),
        [`${newStatus}By`]: userId,
        type: 'initial_value_decision', // Tipo especial para identificar decisões sobre valor inicial
        quantity: Number(quantity) || 1 // 🆕 Quantidade do serviço
      };

      // 🔒 REGRA: Se estamos aceitando o valor inicial, TODAS as contrapropostas devem ser rejeitadas
      const updatedCounterProposals = action === 'accept_initial_value' 
        ? counterProposals.map((proposal: any) => ({
            ...proposal,
            status: 'rejected',
            rejectedAt: new Date().toISOString(),
            rejectedBy: userId
          }))
        : counterProposals;

      // Adicionar a nova "contraproposta" representando a decisão sobre o valor inicial
      updatedCounterProposals.push(initialValueProposal);

      // Atualizar o documento
      await budgetRef.update({
        counterProposals: updatedCounterProposals,
        status: newStatus === 'accepted' ? 'approved' : 'rejected',
        updatedAt: new Date().toISOString(),
        updatedBy: userId
      });

      console.log(`✅ [PUT] Valor inicial ${action === 'accept_initial_value' ? 'aceito' : 'rejeitado'} com sucesso`);

      return NextResponse.json({
        success: true,
        message: `Valor inicial ${action === 'accept_initial_value' ? 'aceito' : 'rejeitado'} com sucesso`
      });
    }

    // ✅ ESTRUTURA HIERÁRQUICA: Atualizar na subcoleção
    if (proposalId) {
      console.log('✏️ [PUT] Atualizando orçamento na subcoleção hierárquica:', {
        proposalId,
        influencerId,
        budgetId
      });

      console.log('✅ [PUT] Processando atualização de orçamento');

      // Atualizar documento específico na subcoleção
      const budgetRef = db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .doc(budgetId);

      const updateFields: any = {
        updatedAt: new Date()
      };

      // Adicionar campos de atualização se fornecidos
      if (updateData.amount !== undefined) updateFields.amount = updateData.amount;
      if (updateData.currency !== undefined) updateFields.currency = updateData.currency;
      if (updateData.description !== undefined) updateFields.description = updateData.description;
      if (updateData.serviceType !== undefined) updateFields.serviceType = updateData.serviceType;
      if (updateData.status !== undefined) updateFields.status = updateData.status;
      if (updateData.userId !== undefined) updateFields.updatedBy = updateData.userId;

      // 🆕 Se está aprovando e há contrapropostas, atualizar status da mais recente
      if (updateData.status === 'approved') {
        const budgetDoc = await budgetRef.get();
        if (budgetDoc.exists) {
          const budgetData = budgetDoc.data();
          if (budgetData?.counterProposals && budgetData.counterProposals.length > 0) {
            // Encontrar a contraproposta mais recente
            const sortedProposals = budgetData.counterProposals.sort((a: any, b: any) => {
              return new Date(b.proposedAt).getTime() - new Date(a.proposedAt).getTime();
            });
            
            // Marcar a mais recente como aceita e as outras como rejeitadas
            const updatedCounterProposals = budgetData.counterProposals.map((proposal: any, index: number) => {
              if (proposal.id === sortedProposals[0].id) {
                return { ...proposal, status: 'accepted', acceptedAt: new Date().toISOString() };
              } else {
                return { ...proposal, status: 'rejected' };
              }
            });
            
            updateFields.counterProposals = updatedCounterProposals;
            console.log('✅ [PUT] Contraproposta mais recente marcada como aceita');
          }
        }
      }

      await budgetRef.update(updateFields);

      console.log('✅ [PUT] Orçamento atualizado na subcoleção');

      // 🔥 CORREÇÃO: Recalcular total da proposta SEMPRE após atualizar orçamento
      try {
        console.log('💰 [PUT] Recalculando total da proposta:', proposalId);
        
        // Usar o método forcado para garantir que seja executado
        const recalcResult = await ProposalService.forceRecalculateProposalTotal(proposalId);
        
        if (recalcResult.success) {
          console.log('✅ [PUT] Total da proposta recalculado:', recalcResult.message);
        } else {
          console.warn('⚠️ [PUT] Erro no recálculo:', recalcResult.message);
        }
      } catch (recalcError) {
        console.warn('⚠️ [PUT] Erro crítico ao recalcular total da proposta:', recalcError);
      }

      return NextResponse.json({
        success: true,
        message: 'Orçamento atualizado na subcoleção hierárquica!'
      });
    } 
    // Senão, atualizar orçamento independente
    else {
      console.log('✏️ [PUT] Atualizando orçamento independente:', {
        influencerId,
        budgetId
      });

      // 🔒 SEGURANÇA: Validação explícita de campos permitidos (sem mass assignment)
      const allowedUpdateFields: any = {
        updatedAt: new Date().toISOString()
      };

      // Adicionar apenas campos validados e permitidos
      if (updateData.amount !== undefined && typeof updateData.amount === 'number') {
        allowedUpdateFields.amount = updateData.amount;
      }
      if (updateData.currency !== undefined && typeof updateData.currency === 'string') {
        allowedUpdateFields.currency = updateData.currency;
      }
      if (updateData.description !== undefined && typeof updateData.description === 'string') {
        allowedUpdateFields.description = updateData.description;
      }
      if (updateData.serviceType !== undefined && typeof updateData.serviceType === 'string') {
        allowedUpdateFields.serviceType = updateData.serviceType;
      }
      if (updateData.status !== undefined && typeof updateData.status === 'string') {
        // Validar status permitidos
        const allowedStatuses = ['draft', 'sent', 'pending', 'negotiating', 'approved', 'accepted', 'rejected', 'expired', 'cancelled'];
        if (allowedStatuses.includes(updateData.status)) {
          allowedUpdateFields.status = updateData.status;
        }
      }

      console.log('🔒 [PUT] Campos validados para atualização:', Object.keys(allowedUpdateFields));

      // 🆕 Se está aprovando e há contrapropostas, atualizar status da mais recente
      if (updateData.status === 'approved') {
        const budgetRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets')
          .doc(budgetId);
          
        const budgetDoc = await budgetRef.get();
        if (budgetDoc.exists) {
          const budgetData = budgetDoc.data();
          if (budgetData?.counterProposals && budgetData.counterProposals.length > 0) {
            // Encontrar a contraproposta mais recente
            const sortedProposals = budgetData.counterProposals.sort((a: any, b: any) => {
              return new Date(b.proposedAt).getTime() - new Date(a.proposedAt).getTime();
            });
            
            // Marcar a mais recente como aceita e as outras como rejeitadas
            const updatedCounterProposals = budgetData.counterProposals.map((proposal: any, index: number) => {
              if (proposal.id === sortedProposals[0].id) {
                return { ...proposal, status: 'accepted', acceptedAt: new Date().toISOString() };
              } else {
                return { ...proposal, status: 'rejected' };
              }
            });
            
            allowedUpdateFields.counterProposals = updatedCounterProposals;
            console.log('✅ [PUT] Contraproposta mais recente marcada como aceita (estrutura independente)');
          }
        }
      }

      // NOVA ESTRUTURA: influencers/{influencerId}/budgets/{platform}
      await db
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .doc(budgetId) // budgetId será o nome da plataforma
        .update(allowedUpdateFields);

      return NextResponse.json({
        success: true,
        message: `Orçamento da plataforma ${budgetId} atualizado com segurança!`
      });
    }

  } catch (error) {
    console.error('Erro ao atualizar orçamento:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const budgetId = searchParams.get('budgetId');
    const proposalId = searchParams.get('proposalId');
    const influencerId = searchParams.get('influencerId');
    const userId = searchParams.get('userId');

    if (!budgetId || !influencerId) {
      return NextResponse.json(
        { error: 'budgetId e influencerId são obrigatórios' },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório para validação de permissões' },
        { status: 400 }
      );
    }

    // ✅ ESTRUTURA HIERÁRQUICA: Excluir da subcoleção
    if (proposalId) {
      console.log('🗑️ [DELETE] Excluindo orçamento da subcoleção hierárquica:', {
        proposalId,
        influencerId,
        budgetId
      });

      console.log('✅ [DELETE] Processando exclusão de orçamento');

      // Excluir documento específico da subcoleção
      await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .doc(budgetId)
        .delete();

      console.log('✅ [DELETE] Orçamento excluído da subcoleção');

      // 🔥 NOVO: Recalcular total da proposta após excluir orçamento
      try {
        console.log('💰 [DELETE] Recalculando total da proposta:', proposalId);
        await ProposalService.recalculateProposalTotal(proposalId);
        console.log('✅ [DELETE] Total da proposta recalculado com sucesso');
      } catch (recalcError) {
        console.warn('⚠️ [DELETE] Erro ao recalcular total da proposta (orçamento excluído mesmo assim):', recalcError);
      }

      return NextResponse.json({
        success: true,
        message: 'Orçamento excluído da subcoleção hierárquica!'
      });
    }
    // Senão, excluir orçamento independente
    else {
      console.log('🗑️ [DELETE] Excluindo orçamento independente:', {
        influencerId,
        budgetId
      });

      // NOVA ESTRUTURA: influencers/{influencerId}/budgets/{platform}
      await db
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .doc(budgetId) // budgetId será o nome da plataforma
        .delete();

      return NextResponse.json({
        success: true,
        message: `Orçamento da plataforma ${budgetId} excluído!`
      });
    }

  } catch (error) {
    console.error('Erro ao excluir orçamento:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 

